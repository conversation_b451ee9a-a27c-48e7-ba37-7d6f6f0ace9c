package com.ztkj.app.mapcore

import android.annotation.SuppressLint
import android.graphics.BitmapFactory
import android.graphics.drawable.BitmapDrawable
import android.util.Log
import android.view.MotionEvent
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import com.esri.arcgisruntime.data.FeatureQueryResult
import com.esri.arcgisruntime.data.QueryParameters
import com.esri.arcgisruntime.data.ServiceFeatureTable
import com.esri.arcgisruntime.geometry.AngularUnit
import com.esri.arcgisruntime.geometry.AngularUnitId
import com.esri.arcgisruntime.geometry.Envelope
import com.esri.arcgisruntime.geometry.GeodeticCurveType
import com.esri.arcgisruntime.geometry.GeodeticDistanceResult
import com.esri.arcgisruntime.geometry.Geometry
import com.esri.arcgisruntime.geometry.GeometryEngine
import com.esri.arcgisruntime.geometry.LinearUnit
import com.esri.arcgisruntime.geometry.LinearUnitId
import com.esri.arcgisruntime.geometry.Point
import com.esri.arcgisruntime.geometry.SpatialReferences
import com.esri.arcgisruntime.layers.FeatureLayer
import com.esri.arcgisruntime.layers.Layer
import com.esri.arcgisruntime.layers.WmsLayer
import com.esri.arcgisruntime.loadable.LoadStatus
import com.esri.arcgisruntime.mapping.ArcGISMap
import com.esri.arcgisruntime.mapping.Viewpoint
import com.esri.arcgisruntime.mapping.view.DefaultMapViewOnTouchListener
import com.esri.arcgisruntime.mapping.view.Graphic
import com.esri.arcgisruntime.mapping.view.GraphicsOverlay
import com.esri.arcgisruntime.mapping.view.IdentifyLayerResult
import com.esri.arcgisruntime.mapping.view.MapRotationChangedEvent
import com.esri.arcgisruntime.mapping.view.MapRotationChangedListener
import com.esri.arcgisruntime.mapping.view.MapView
import com.esri.arcgisruntime.ogc.wfs.OgcAxisOrder
import com.esri.arcgisruntime.ogc.wfs.WfsFeatureTable
import com.esri.arcgisruntime.symbology.PictureMarkerSymbol
import com.ztkj.app.mapcore.entity.GeoJsonModel
import com.ztkj.app.mapcore.util.JsonUtils
import com.ztkj.app.mapcore.util.SymbolUtil
import com.ztkj.baselib.base.appContext
import com.ztkj.baselib.ext.util.notNull
import com.ztkj.baselib.ext.util.toJson


/**
 * @CreateTime : 2023/3/20 16:51
 * <AUTHOR> AppOS
 * @Description :
 */
class Map2DHelper : BaseMapHelper {

    private lateinit var mapView: MapView

    private var hammerGraphic: Graphic? = null

    private lateinit var hammerGraphicsOverlay: GraphicsOverlay

    private lateinit var wfsGraphicsOverlay: GraphicsOverlay

    private lateinit var featureGraphicsOverlay: GraphicsOverlay

    private lateinit var nearbyHammerGraphicsOverlay: GraphicsOverlay

    private lateinit var highLightGraphicsOverlay: GraphicsOverlay

    companion object {
        private const val TAG = "Map2DHelper"

        const val MIN_SCALE = 10.0

        const val MAX_SCALE = 564.0
    }

    override fun init(mapView: MapView) {
        this.mapView = mapView
        this.mapView.run {
            map = ArcGISMap()
            map.maxScale = MIN_SCALE
            map.minScale = MAX_SCALE
            isAttributionTextVisible = false
        }
        initParams()
    }

    private fun initParams() {
        wfsGraphicsOverlay = GraphicsOverlay()
        mapView.graphicsOverlays.add(wfsGraphicsOverlay)
        featureGraphicsOverlay = GraphicsOverlay()
        mapView.graphicsOverlays.add(featureGraphicsOverlay)
        nearbyHammerGraphicsOverlay = GraphicsOverlay()
        mapView.graphicsOverlays.add(nearbyHammerGraphicsOverlay)
        hammerGraphicsOverlay = GraphicsOverlay()
        mapView.graphicsOverlays.add(hammerGraphicsOverlay)
        highLightGraphicsOverlay = GraphicsOverlay()
        mapView.graphicsOverlays.add(highLightGraphicsOverlay)
    }

    /**
     * 这里定义了一个mMapView属性，用来实现自定义扩展地图方法
     */
    override val mMapView: MapView
        get() = this.mapView

    override fun setMapCenter(point: Point, scale: Double) {
        mapView.setViewpointCenterAsync(point, scale)
    }

    override fun getMapCenter(): Point {
        val visibleViewpoint = mapView.getCurrentViewpoint(Viewpoint.Type.CENTER_AND_SCALE)
        // 获取可见区域的中心点坐标
        return visibleViewpoint.targetGeometry.extent.center
    }

    override fun mapZoomIn(interval: Double) {
        val scale = mapView.mapScale - interval
        setMapCenter(scale)
    }

    override fun mapZoomOut(interval: Double) {
        val scale = mapView.mapScale + interval
        setMapCenter(scale)
    }

    private fun setMapCenter(scale: Double) {
        if (scale < MIN_SCALE || scale > MAX_SCALE)
            return
        mapView.setViewpointAsync(Viewpoint(getMapCenter(), scale))
    }

    override fun loadFeatureLayer(url: String?) {
        url?.run {
            val serviceFeatureTable = ServiceFeatureTable(this)
            val featureLayer = FeatureLayer(serviceFeatureTable)
            featureLayer.loadAsync()
            featureLayer.addLoadStatusChangedListener {
                Log.e(TAG, "loadFeatureLayer: ${it.newLoadStatus}")
            }
            addOperationLayer(featureLayer)
        }
    }

    override fun loadWmsLayer(url: String, names: List<String>) {
        loadRefreshWmsLayer(url, names, null)
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun loadRefreshWmsLayer(
        url: String,
        names: List<String>,
        refreshIntervalMilliseconds: Long?
    ) {
        loadRefreshWmsLayer(url, names, refreshIntervalMilliseconds, null)
    }

    override fun loadRefreshWmsLayer(
        url: String,
        names: List<String>,
        refreshIntervalMilliseconds: Long?,
        cqlFilter: String?
    ) {
        val wmsLayer = WmsLayer(url, names)
        cqlFilter?.run { wmsLayer.customParameters["CQL_FILTER"] = this }
        wmsLayer.loadAsync()
        refreshIntervalMilliseconds?.run { wmsLayer.refreshInterval = this }
        wmsLayer.addLoadStatusChangedListener { loadStatusChangedEvent ->
            if (loadStatusChangedEvent.newLoadStatus != LoadStatus.LOADED) {
                val error = loadStatusChangedEvent.newLoadStatus
                if (error != null) {
                    Log.e("WMSLayer", "Failed to load WMSLayer: $error")
                }
            }

        }
        addOperationLayer(wmsLayer)
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun setMapOnTouchCallback(listener: MapQueryWmsResultListener) {
        mapView.onTouchListener =
            object : DefaultMapViewOnTouchListener(mapView.context, mapView) {
                override fun onRotate(event: MotionEvent?, rotationAngle: Double): Boolean {
                    //禁止地图旋转
                    return false
                }

                /*                override fun onSingleTapConfirmed(event: MotionEvent): Boolean {
                                    //屏幕坐标点
                                    val screenPoint = android.graphics.Point(event.x.toInt(), event.y.toInt())
                                    Log.e("123", "screenPoint:$screenPoint")
                                    identify(screenPoint, listener)
                                    identifyFeatures(screenPoint)
                                    return super.onSingleTapConfirmed(event)
                                }*/
            }
    }

    private fun identifyFeatures(screenPoint: android.graphics.Point) {
        val tolerance = 10
        val returnPopupsOnly = false
        val maxResults = 5

        val operationalLayers = mapView.map.operationalLayers
        val wmsLayer = operationalLayers.first()

        val identifyLayerAsync = mapView.identifyLayerAsync(
            wmsLayer,
            screenPoint,
            tolerance.toDouble(),
            returnPopupsOnly,
            maxResults
        )
        identifyLayerAsync.addDoneListener {
            try {
                val identifyLayerResult: IdentifyLayerResult = identifyLayerAsync.get()
                if (identifyLayerResult.elements.isNotEmpty()) {
                    for (i in identifyLayerResult.elements) {
//                        val identifiedFeature = identifyLayerResult.elements[0]
                        // 在此处理识别到的要素
                        Log.d("Identified Feature", i.toJson())
                    }
                } else {
                    Log.d("Identified Feature", "No features identified.")
                }
            } catch (e: Exception) {
                Log.e("Identify Features", "Error identifying features: ${e.message}")
            }
        }
    }


    private fun identify(screenPoint: android.graphics.Point, listener: MapQueryWmsResultListener) {
        val point = mapView.screenToLocation(screenPoint)

        try {
            //要素层没有识别的结果，需要在专题图wms层查询
            val operationalLayers = mapView.map.operationalLayers
            if (operationalLayers.isNotEmpty()) {
                //选择最上面的专题图层
                val layer = operationalLayers.first()
                Log.e(TAG, "identify: $layer , ${layer.name} , ${layer.id}")
                if (layer is WmsLayer) {
                    val layerName = layer.getName()
                    //创建并返回一个几何对象，该对象表示在给定几何周围指定距离处相对于给定几何的缓冲区多边形。
                    val geometry: Geometry = GeometryEngine.buffer(point, 4.0)
                    //获取一个表示此 Geometry 的空间范围的 Envelope
                    val extent = geometry.extent
                    val bboxStr =
                        extent.xMin.toString() + "," + extent.yMin + "," + extent.xMax + "," + extent.yMax
                    //查询此wms层触摸点的geoJson数据
                    Log.e(TAG, "identify: ${bboxStr}")
                    listener?.invoke(point, layerName, bboxStr)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun loadWfsLayer(url: String, tableName: String) {

        val wfsFeatureTable = WfsFeatureTable(url, tableName)
        wfsFeatureTable.featureRequestMode = ServiceFeatureTable.FeatureRequestMode.MANUAL_CACHE
        wfsFeatureTable.axisOrder = OgcAxisOrder.NO_SWAP
        wfsFeatureTable.loadAsync()

        wfsFeatureTable.addLoadStatusChangedListener {
            if (it.newLoadStatus != LoadStatus.LOADED) {
                Log.e(TAG, "wfsFeatureTable: 加载失败 ")
                val loadError = wfsFeatureTable.loadError
                Log.e(TAG, "wfsFeatureTable 错误原因: ${loadError?.message}")
            } else {
                Log.e(TAG, "wfsFeatureTable: 加载成功")
            }
        }

        val featureLayer = FeatureLayer(wfsFeatureTable)
        featureLayer.loadAsync()

        featureLayer.addDoneLoadingListener {
            if (featureLayer.loadStatus == LoadStatus.LOADED) {
                Log.e(TAG, "featureLayer:  加载成功")
                // create a query based on the current visible extent
                val wfsQuery = QueryParameters()
                wfsQuery.geometry = mapView.visibleArea.extent
                // choose the spatial relationship used for the query
                wfsQuery.spatialRelationship = QueryParameters.SpatialRelationship.INTERSECTS
                // populate the WFS feature table based on the given extent
                val future =
                    wfsFeatureTable.populateFromServiceAsync(wfsQuery, false, null)
                future.addDoneListener {
                    try {
                        val result: FeatureQueryResult = future.get()
                        wfsGraphicsOverlay.graphics.clear()
                        Log.d(TAG, "loadWfsLayer result count: ${result.count()}")
                        for (feature in result) {
//                            Log.e(TAG, "feature attributes: ${feature.attributes.toJson()}")
                            val hiltCount = feature.attributes["hit_count"].toString()
                            val graphic = Graphic(
                                feature.geometry,
                                SymbolUtil.buildCompositeSymbol(hiltCount)
                            )

                            wfsGraphicsOverlay.graphics.add(graphic)
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }

            } else {
                Log.e(TAG, "wfs ${url} 加载失败: ")
            }
        }
    }

    override fun loadWfsLayer(
        url: String,
        tableName: String,
        longitude: Double,
        latitude: Double,
        callback: (Map<String, Any>?) -> Unit
    ) {
        val wfsFeatureTable = WfsFeatureTable(url, tableName)
        wfsFeatureTable.featureRequestMode = ServiceFeatureTable.FeatureRequestMode.MANUAL_CACHE
        wfsFeatureTable.axisOrder = OgcAxisOrder.NO_SWAP
        wfsFeatureTable.loadAsync()

        wfsFeatureTable.addLoadStatusChangedListener {
            if (it.newLoadStatus != LoadStatus.LOADED) {
                Log.e(TAG, "wfsFeatureTable: 加载失败 ")
                val loadError = wfsFeatureTable.loadError
                Log.e(TAG, "wfsFeatureTable 错误原因: ${loadError?.message}")
                // 修复：WFS表加载失败时调用callback返回null
                callback.invoke(null)
                return@addLoadStatusChangedListener
            } else {
                Log.d(TAG, "wfsFeatureTable: 加载成功")
            }
        }

        val featureLayer = FeatureLayer(wfsFeatureTable)
        featureLayer.loadAsync()

        featureLayer.addDoneLoadingListener {
            if (featureLayer.loadStatus == LoadStatus.LOADED) {
                val radiusInMeters = 0.8
                // 计算查询范围的几何图形（圆）
                val centerPoint =
                    Point(longitude, latitude, SpatialReferences.getWgs84())

                val bufferGeometry = GeometryEngine.bufferGeodetic(
                    centerPoint,
                    radiusInMeters,
                    LinearUnit(LinearUnitId.METERS),
                    Double.NaN,
                    GeodeticCurveType.GEODESIC
                )

                Log.d(TAG, "featureLayer: 加载成功")
                Log.d(TAG, "WFS查询参数 - 中心点: (${longitude}, ${latitude}), 查询半径: ${radiusInMeters}m")
                Log.d(TAG, "WFS查询几何范围: ${bufferGeometry.extent}")

                // create a query based on the current visible extent
                val wfsQuery = QueryParameters()
                wfsQuery.geometry = bufferGeometry
                // choose the spatial relationship used for the query
                wfsQuery.spatialRelationship = QueryParameters.SpatialRelationship.INTERSECTS
                // populate the WFS feature table based on the given extent
                val future =
                    wfsFeatureTable.populateFromServiceAsync(wfsQuery, false, null)
                future.addDoneListener {
                    try {
                        val result: FeatureQueryResult = future.get()
                        wfsGraphicsOverlay.graphics.clear()
                        var closestDistance = Double.MAX_VALUE
                        var closestAttributes: Map<String, Any>? = null
                        Log.d(TAG, "WFS查询结果 - 找到要素数量: ${result.count()}")

                        // 修复：如果没有查询结果，直接返回null
                        if (result.count() == 0) {
                            Log.d(TAG, "loadWfsLayer: 在指定范围内未找到任何要素")
                            callback.invoke(null)
                            return@addDoneListener
                        }

                        var featureIndex = 0
                        for (feature in result) {
                            featureIndex++
                            val pointCode = feature.attributes["point_code"]?.toString() ?: "未知"
                            val geometry = feature.geometry
                            if (geometry != null) {
                                // 修复：对于点几何图形，直接使用点坐标；对于其他几何图形，使用质心
                                val targetPoint = geometry as? Point ?: geometry.extent.center

                                val distance = GeometryEngine.distanceGeodetic(
                                    centerPoint,
                                    targetPoint,
                                    LinearUnit(LinearUnitId.METERS),
                                    AngularUnit(AngularUnitId.DEGREES),
                                    GeodeticCurveType.GEODESIC
                                ).distance

                                // 详细日志：打印每个要素的信息
                                Log.d(TAG, "要素${featureIndex} - point_code: ${pointCode}")
                                Log.d(TAG, "要素${featureIndex} - 坐标: (${targetPoint.x}, ${targetPoint.y})")
                                Log.d(TAG, "要素${featureIndex} - 距离中心点距离: ${String.format("%.3f", distance)}m")
                                Log.d(TAG, "要素${featureIndex} - 是否在${radiusInMeters}m范围内: ${distance <= radiusInMeters}")

                                val graphic = Graphic(
                                    geometry,
//                                    SymbolUtil.buildTampingDesignPoint(pointName)
                                    SymbolUtil.buildSimpleTampingDesignPoint()
                                )

                                // 由于使用了0.8米缓冲区查询，理论上所有返回的要素都应该在0.8米范围内
                                // 但实际计算距离来验证
                                if (distance < closestDistance) {
                                    closestDistance = distance
                                    closestAttributes = feature.attributes
                                    Log.d(TAG, "更新最近要素 - point_code: ${pointCode}, 距离: ${String.format("%.3f", distance)}m")
                                }
                                wfsGraphicsOverlay.graphics.add(graphic)
                            }
                        }

                        // 计算所有要素之间的距离
                        if (result.count() > 1) {
                            Log.d(TAG, "=== 计算要素间距离 ===")
                            val featureList = result.toList()
                            for (i in featureList.indices) {
                                for (j in i + 1 until featureList.size) {
                                    val feature1 = featureList[i]
                                    val feature2 = featureList[j]
                                    val point1 = feature1.geometry as? Point ?: feature1.geometry.extent.center
                                    val point2 = feature2.geometry as? Point ?: feature2.geometry.extent.center

                                    val distanceBetween = GeometryEngine.distanceGeodetic(
                                        point1,
                                        point2,
                                        LinearUnit(LinearUnitId.METERS),
                                        AngularUnit(AngularUnitId.DEGREES),
                                        GeodeticCurveType.GEODESIC
                                    ).distance

                                    val pointCode1 = feature1.attributes["point_code"]?.toString() ?: "未知"
                                    val pointCode2 = feature2.attributes["point_code"]?.toString() ?: "未知"

                                    Log.d(TAG, "要素间距离 - ${pointCode1} 到 ${pointCode2}: ${String.format("%.3f", distanceBetween)}m")
                                }
                            }
                        }

                        Log.d(TAG, "最终选择的最近要素 - point_code: ${closestAttributes?.get("point_code")}, 距离: ${String.format("%.3f", closestDistance)}m")
                        callback.invoke(closestAttributes)
                    } catch (e: Exception) {
                        Log.e(TAG, "loadWfsLayer 查询异常: ${e.message}", e)
                        // 修复：异常时也要调用callback
                        callback.invoke(null)
                    }
                }

            } else {
                Log.e(TAG, "wfs $url 加载失败: ")
                // 修复：FeatureLayer加载失败时调用callback返回null
                callback.invoke(null)
            }
        }
    }

    override fun loadNearestTampingPoint(
        url: String,
        tableName: String,
        callback: (Map<String, Any>?) -> Unit
    ) {
        val wfsFeatureTable = WfsFeatureTable(url, tableName)
        wfsFeatureTable.featureRequestMode = ServiceFeatureTable.FeatureRequestMode.MANUAL_CACHE
        wfsFeatureTable.axisOrder = OgcAxisOrder.NO_SWAP
        wfsFeatureTable.loadAsync()

        wfsFeatureTable.addLoadStatusChangedListener {
            if (it.newLoadStatus != LoadStatus.LOADED) {
                Log.e(TAG, "wfsFeatureTable: 加载失败 ")
                val loadError = wfsFeatureTable.loadError
                Log.e(TAG, "wfsFeatureTable 错误原因: ${loadError?.message}")
            } else {
                Log.d(TAG, "wfsFeatureTable: 加载成功")
            }
        }

        val featureLayer = FeatureLayer(wfsFeatureTable)
        featureLayer.loadAsync()

        featureLayer.addDoneLoadingListener {
            if (featureLayer.loadStatus == LoadStatus.LOADED) {
                val radiusInMeters = 2.0
                // 计算查询范围的几何图形（圆）
                val point = getMapCenter()
                val centerPoint = Point(point.x, point.y, SpatialReferences.getWgs84())

                val bufferGeometry = GeometryEngine.bufferGeodetic(
                    centerPoint,
                    radiusInMeters,
                    LinearUnit(LinearUnitId.METERS),
                    Double.NaN,
                    GeodeticCurveType.GEODESIC
                )

                Log.d(TAG, "featureLayer:  加载成功")
                // create a query based on the current visible extent
                val wfsQuery = QueryParameters()
                wfsQuery.geometry = bufferGeometry
                // choose the spatial relationship used for the query
                wfsQuery.spatialRelationship = QueryParameters.SpatialRelationship.INTERSECTS
                // populate the WFS feature table based on the given extent
                val future =
                    wfsFeatureTable.populateFromServiceAsync(wfsQuery, false, null)
                future.addDoneListener {
                    try {
                        val result: FeatureQueryResult = future.get()
                        var closestDistance = Double.MAX_VALUE
                        var closestAttributes: Map<String, Any>? = null
                        Log.d(TAG, "loadWfsLayer result count: ${result.count()}")
                        for (feature in result) {
//                            Log.e(TAG, "feature attributes: ${feature.attributes.toJson()}")
                            val geometry = feature.geometry
                            if (geometry != null) {
                                val distance = GeometryEngine.distanceGeodetic(
                                    centerPoint,
                                    geometry.extent.center,
                                    LinearUnit(LinearUnitId.METERS),
                                    AngularUnit(AngularUnitId.DEGREES),
                                    GeodeticCurveType.GEODESIC
                                ).distance
                                if (distance < closestDistance) {
                                    closestDistance = distance
                                    closestAttributes = feature.attributes
                                }
                            }
                        }
                        callback.invoke(closestAttributes)
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }

            } else {
                Log.e(TAG, "wfs ${url} 加载失败: ")
            }
        }
    }

    override fun loadNearbyTampingPointList(
        url: String,
        tableName: String,
        callback: (FeatureQueryResult) -> Unit
    ) {
        val wfsFeatureTable = WfsFeatureTable(url, tableName)
        wfsFeatureTable.featureRequestMode = ServiceFeatureTable.FeatureRequestMode.MANUAL_CACHE
        wfsFeatureTable.axisOrder = OgcAxisOrder.NO_SWAP
        wfsFeatureTable.loadAsync()

        wfsFeatureTable.addLoadStatusChangedListener {
            if (it.newLoadStatus != LoadStatus.LOADED) {
                Log.e(TAG, "wfsFeatureTable: 加载失败 ")
                val loadError = wfsFeatureTable.loadError
                Log.e(TAG, "wfsFeatureTable 错误原因: ${loadError?.message}")
            } else {
                Log.d(TAG, "wfsFeatureTable: 加载成功")
            }
        }

        val featureLayer = FeatureLayer(wfsFeatureTable)
        featureLayer.loadAsync()

        featureLayer.addDoneLoadingListener {
            if (featureLayer.loadStatus == LoadStatus.LOADED) {
                val radiusInMeters = 1.0
                // 计算查询范围的几何图形（圆）
                val point = getMapCenter()
                val centerPoint = Point(point.x, point.y, SpatialReferences.getWgs84())

                val bufferGeometry = GeometryEngine.bufferGeodetic(
                    centerPoint,
                    radiusInMeters,
                    LinearUnit(LinearUnitId.METERS),
                    Double.NaN,
                    GeodeticCurveType.GEODESIC
                )
                // create a query based on the current visible extent
                val wfsQuery = QueryParameters()
                wfsQuery.geometry = bufferGeometry
                // choose the spatial relationship used for the query
                wfsQuery.spatialRelationship = QueryParameters.SpatialRelationship.INTERSECTS
                // populate the WFS feature table based on the given extent
                val future =
                    wfsFeatureTable.populateFromServiceAsync(wfsQuery, false, null)
                future.addDoneListener {
                    try {
                        val result: FeatureQueryResult = future.get()
                        callback.invoke(result)
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }

            } else {
                Log.e(TAG, "wfs ${url} 加载失败: ")
            }
        }
    }

    override fun drawHighlightPoint(text: String?, geometry: Geometry) {
        if (text == null) {
            return
        }
        val graphic = Graphic(
            geometry,
            SymbolUtil.buildHighLightPoint(text)
        )
        highLightGraphicsOverlay.clearSelection()
        highLightGraphicsOverlay.graphics.add(graphic)
    }


    fun loadWfsLayer2(url: String, tableName: String) {

        wfsGraphicsOverlay.graphics.clear()

        val wfsFeatureTable = WfsFeatureTable(url, tableName)
        wfsFeatureTable.featureRequestMode = ServiceFeatureTable.FeatureRequestMode.MANUAL_CACHE
        wfsFeatureTable.axisOrder = OgcAxisOrder.NO_SWAP
        wfsFeatureTable.loadAsync()

        wfsFeatureTable.addLoadStatusChangedListener {
            if (it.newLoadStatus != LoadStatus.LOADED) {
                Log.e(TAG, "wfsFeatureTable: 加载失败 ")
                val loadError = wfsFeatureTable.loadError
                Log.e(TAG, "wfsFeatureTable 错误原因: ${loadError?.message}")
            } else {
                Log.e(TAG, "wfsFeatureTable: 加载成功")
            }
        }

        val featureLayer = FeatureLayer(wfsFeatureTable)
        featureLayer.loadAsync()
        featureLayer.addDoneLoadingListener {
            if (featureLayer.loadStatus == LoadStatus.LOADED) {
                Log.e(TAG, "featureLayer:  加载成功")
                // create a query based on the current visible extent
                val wfsQuery = QueryParameters()
                wfsQuery.geometry = featureLayer.fullExtent
                // choose the spatial relationship used for the query
                wfsQuery.spatialRelationship = QueryParameters.SpatialRelationship.INTERSECTS
                // populate the WFS feature table based on the given extent
                val future =
                    wfsFeatureTable.populateFromServiceAsync(wfsQuery, false, null)
                future.addDoneListener {
                    try {
                        val result: FeatureQueryResult = future.get()
                        Log.d(TAG, "loadWfsLayer result count: ${result.count()}")
                        for (feature in result) {
//                            Log.e(TAG, "feature attributes: ${feature.attributes.toJson()}")
                            val hiltCount = feature.attributes["hit_count"].toString()
                            val graphic = Graphic(
                                feature.geometry,
                                SymbolUtil.buildCompositeSymbol(hiltCount)
                            )
                            wfsGraphicsOverlay.graphics.add(graphic)
                        }
//                        mapView.setViewpointAsync(Viewpoint(result.last().geometry.extent))
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }

            } else {
                Log.e(TAG, "wfs ${url} 加载失败: ")
            }
        }
//        addOperationLayer(featureLayer)
    }


    override fun drawHammer(point: Point, resId: Int, angle: Float) {
//        var isNeedUpdate = true
        hammerGraphic.notNull({
/*            val distanceResult: GeodeticDistanceResult = GeometryEngine.distanceGeodetic(
                point, it.geometry.extent.center, LinearUnit(
                    LinearUnitId.METERS
                ), AngularUnit(AngularUnitId.DEGREES),
                GeodeticCurveType.GEODESIC
            )
            val distance = distanceResult.distance*/
            it.geometry = point
            //当距离大于1M的时候才变化,去掉浮动
            /*            if (distance > 0.01) {
                            isNeedUpdate = true
                            it.geometry = point
                        } else {
                            isNeedUpdate = false
                        }*/
            //TODO  这里应该要增加角度变化的阈值
            /*            val symbol = it.symbol
                        if (symbol is PictureMarkerSymbol) {
                            symbol.angle = angle
                        }*/
        }, {
            val bitmap = BitmapFactory.decodeResource(appContext.resources, resId)
            val bitmapDrawable = BitmapDrawable(appContext.resources, bitmap)
            val pictureMarkerSymbol = PictureMarkerSymbol.createAsync(bitmapDrawable).get()
            pictureMarkerSymbol.angle = angle
            hammerGraphic = Graphic(point, pictureMarkerSymbol)
        })

        hammerGraphicsOverlay.graphics.clear()
        hammerGraphicsOverlay.graphics.add(hammerGraphic)
        mapView.setViewpointAsync(Viewpoint(point, mapView.mapScale))
        /*        if (isNeedUpdate) {
        //            hammerGraphicsOverlay.graphics.clear()
        //            hammerGraphicsOverlay.graphics.add(hammerGraphic)
                    mapView.setViewpointAsync(Viewpoint(point, mapView.mapScale))
                }*/
    }

    override fun loadNearbyDesignPoint(geoJsonModel: GeoJsonModel) {
        val parseGeoJson = parseGeoJson(geoJsonModel)
        parseGeoJson?.run {
            nearbyHammerGraphicsOverlay.clearSelection()
            nearbyHammerGraphicsOverlay.graphics.addAll(this)
        }
    }


    private fun parseGeoJson(geoJsonModel: GeoJsonModel): List<Graphic>? {
        if (!geoJsonModel.type.equals("FeatureCollection")) {
            return null
        }

        val list = mutableListOf<Graphic>()
        for (feature in geoJsonModel.features) {
//            val type = feature.type
            val graphic = createGraphic(feature)
            list.add(graphic)
        }
        return list
    }

    private fun createGraphic(feature: GeoJsonModel.FeaturesModel): Graphic {
        //获取点坐标的集合
        val pointList = feature.geometry.coordinates as List<*>
        //根据坐标和坐标系生成点集合对象
        val geometry = Point(
            pointList[0].toString().toDouble(),
            pointList[1].toString().toDouble(),
            SpatialReferences.getWgs84()
        )
        //点符号
        val symbol = SymbolUtil.pointSymbol
        return if (feature.properties == null) {
            //将属性对象转map
            val map: Map<String, Any> = JsonUtils.objToMap(feature.properties)
            //根据geometry,属性map，符号创建Graphic
            Graphic(geometry, map, symbol)
        } else {
            Graphic(geometry, symbol)
        }
    }


    @SuppressWarnings("unused")
    private fun populateFromServer(wfsFeatureTable: WfsFeatureTable, extent: Envelope) {
        // create a query based on the current visible extent
        val visibleExtentQuery = QueryParameters()
        visibleExtentQuery.geometry = extent
        visibleExtentQuery.spatialRelationship = QueryParameters.SpatialRelationship.INTERSECTS
        // populate the WFS feature table based on the current extent
        wfsFeatureTable.populateFromServiceAsync(visibleExtentQuery, false, null)
    }


    private fun addOperationLayer(layer: Layer) {
        mapView.map.operationalLayers.add(layer)
    }


    /**
     * 判断地图是否初始化
     */
    private fun isMapInitialized(): Boolean {
        return this::mapView.isInitialized
    }


    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        if (!isMapInitialized()) return
        when (event) {
            Lifecycle.Event.ON_RESUME -> {
                mapView.resume()
            }

            Lifecycle.Event.ON_PAUSE -> {
                mapView.pause()
            }

            Lifecycle.Event.ON_DESTROY -> {
                mapView.dispose()
            }

            else -> {}
        }

    }

}