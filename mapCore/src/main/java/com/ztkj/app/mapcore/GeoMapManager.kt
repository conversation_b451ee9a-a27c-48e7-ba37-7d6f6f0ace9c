package com.ztkj.app.mapcore


import com.esri.arcgisruntime.ArcGISRuntimeEnvironment

/**
 * @CreateTime : 2023/3/20 16:52
 * <AUTHOR> AppOS
 * @Description :
 */
object GeoMapManager {

    private const val ArcGisApiKey = "runtimelite,1000,rud9978704000,none,TRB3LNBHPFMB4P7EJ046"

    enum class MapType {
        Map2D,
        Map3D
    }

    fun buildHelper(mapType: MapType): BaseMapHelper {
        ArcGISRuntimeEnvironment.setLicense(ArcGisApiKey)
        return when(mapType) {
            MapType.Map2D -> Map2DHelper()
            MapType.Map3D -> throw IllegalArgumentException("未实现的方法")
        }
    }
}