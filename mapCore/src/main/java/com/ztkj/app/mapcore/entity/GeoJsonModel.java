package com.ztkj.app.mapcore.entity;

import java.util.ArrayList;
import java.util.List;

/**
 * @CreateTime : 2022/8/2 10:48
 * <AUTHOR> AppOS
 * @Description : 封装的GeoJson模型
 */
public class GeoJsonModel {

    public String type;  //FeatureCollection

    public GeoJsonModel(String type, List<FeaturesModel> features) {
        this.type = type;
        this.features = features;
    }

    public GeoJsonModel() {
    }

    public List<FeaturesModel> features;

    public static class FeaturesModel {

        public String type;  //Feature
        public String id;
        public GeometryBean geometry;     //几何对象
        public Object properties;         //属性

        public static class GeometryBean {
            public String type;      //几何对象类型
            public List coordinates; //坐标
        }

    }

    /**
     * geoJson几何对象的类型
     */
    public static class GeometryType {
        /**
         * 点(一维数组)
         */
        public static final String Point = "Point";
        /**
         * 多点(二维数组)
         */
        public static final String MultiPoint = "MultiPoint";
        /**
         * 线(二维数组)
         */
        public static final String LineString = "LineString";
        /**
         * 多线(三维数组)
         */
        public static final String MultiLineString = "MultiLineString";
        /**
         * 多边形(三维数组)
         */
        public static final String Polygon = "Polygon";
        /**
         * 多多边形(四维数组)
         */
        public static final String MultiPolygon = "MultiPolygon";
    }

    /**
     * 生成FeatureCollection的GeoJsonModel
     *
     * @param featureList
     * @param geometryType
     * @param propertiesList
     * @param <T>
     * @return
     */
    public static <T> GeoJsonModel buildFeatureCollection(List<T> featureList, String geometryType, List<Object> propertiesList) {
        GeoJsonModel model = new GeoJsonModel();
        model.type = "FeatureCollection";

        List<FeaturesModel> features = new ArrayList<>();

        for (int i = 0; i < featureList.size(); i++) {
            List coordinates = (List) featureList.get(i);
            Object properties = propertiesList.get(i);

            FeaturesModel featuresModel = new FeaturesModel();
            featuresModel.type = "Feature";
            featuresModel.properties = properties;

            FeaturesModel.GeometryBean geometryBean = new FeaturesModel.GeometryBean();
            geometryBean.type = geometryType;
            geometryBean.coordinates = coordinates;
            featuresModel.geometry = geometryBean;
            features.add(featuresModel);
        }

        model.features = features;
        return model;
    }

    /**
     * 生成单Feature的GeoJsonModel
     *
     * @param coordinates
     * @param geometryType
     * @param properties
     * @param <T>
     * @return
     */
    public static <T> GeoJsonModel buildFeature(List<T> coordinates, String geometryType, Object properties) {

        GeoJsonModel model = new GeoJsonModel();
        model.type = "FeatureCollection";

        List<FeaturesModel> features = new ArrayList<>();

        FeaturesModel featuresModel = new FeaturesModel();
        featuresModel.type = "Feature";
        featuresModel.properties = properties;

        FeaturesModel.GeometryBean geometryBean = new FeaturesModel.GeometryBean();
        geometryBean.type = geometryType;
        geometryBean.coordinates = coordinates;

        featuresModel.geometry = geometryBean;

        features.add(featuresModel);
        model.features = features;
        return model;
    }
}
