package com.ztkj.baselib.ext.lifecycle

import androidx.lifecycle.*
import com.ztkj.baselib.callback.livedata.BooleanLiveData

/**
 * @CreateTime : 2022/10/12 11:17
 * <AUTHOR> AppOS
 * @Description :
 */
object KtxAppLifeObserver : LifecycleEventObserver {

    var isForeground = BooleanLiveData()

    //在前台
    private  fun onForeground() {
        isForeground.value = true
    }

    //在后台
    private fun onBackground() {
        isForeground.value = false
    }

    /**
     * Called when a state transition event happens.
     *
     * @param source The source of the event
     * @param event The event
     */
    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        when(event){
            Lifecycle.Event.ON_START -> onForeground()
            Lifecycle.Event.ON_STOP -> onBackground()
            else -> {}
        }
    }

}