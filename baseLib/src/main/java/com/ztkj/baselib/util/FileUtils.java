package com.ztkj.baselib.util;

import android.content.Context;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.webkit.MimeTypeMap;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

public class FileUtils {

    /**
     * 获取文件的文件名
     *
     * @param path
     * @return
     */
    public static String getFileName(String path) {
        String[] split = path.split("/");
        if (split.length > 0) {
            return split[split.length - 1];
        } else {
            return path;
        }
    }

    /**
     * 获取文件类型(文件扩展名)
     *
     * @param path
     * @return
     */
    public static String getFileType(String path) {
        return MimeTypeMap.getFileExtensionFromUrl(path);
//        String fileName = getFileName(path);
//        //小数点属于特殊字符,需要转译
//        String[] split = fileName.split("\\.");
//        if (split.length > 0) {
//            return split[split.length - 1];
//        } else {
//            return path;
//        }
    }

    /**
     * 获取文件类型
     *
     * @param uri
     * @return
     */
    public static String getFileType(Uri uri) {
        return getFileType(uri.toString());
    }

    /**
     * 获取app文件根目录
     *
     * @param context
     * @return
     */
    public static String getFileDir(Context context) {
        if (checkExternalStorageState()) {
            return context.getExternalFilesDir(null).getAbsolutePath();
        } else {
            return context.getFilesDir().getAbsolutePath();
        }
    }

    /**
     * 判断外部存储是否可用
     *
     * @return
     */
    private static boolean checkExternalStorageState() {
        return Environment.MEDIA_MOUNTED.equals(Environment.getExternalStorageState());
    }

    /**
     * 文件复制
     * 如果输出文件在外部存储,android版本大于10则需要拥有所有文件的读写权限
     *
     * @param is
     * @param fos
     */
    public static void copy(InputStream is, OutputStream fos) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                android.os.FileUtils.copy(is, fos);
            } else {
                byte[] buffer = new byte[8 * 1024];
                int byteCount;
                while ((byteCount = is.read(buffer)) != -1) {
                    fos.write(buffer, 0, byteCount);
                }
                fos.flush();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 根据uri获取路径
     *
     * @param context
     * @param uri
     * @return
     */
    public static String getUriPath(Context context, Uri uri) {
        return FileUriToPath.getFilePath(context, uri);
    }



}
