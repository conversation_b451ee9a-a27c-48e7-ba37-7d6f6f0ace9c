package com.ztkj.baselib.util;

import java.io.File;
import java.io.FileFilter;
import java.util.regex.Pattern;

/**
 * CPU帮助类
 */

public class CpuHelp {

    /**
     * 检查文件名是否是“cpu”,紧随其后的是一个单一的数字号码
     */
    private static String matches = "cpu[0-9]";
    private static String TAG = "CPUHelp";

    /**
     * CPU个数
     *
     * @return
     */
    public static int getNumCores() {
        /**
         * 私有类只显示CPU设备目录清单
         */
        class CpuFilter implements FileFilter {
            @Override
            public boolean accept(File pathname) {
                //检查文件名是否是“cpu”,紧随其后的是一个单一的数字号码
                if (Pattern.matches(matches, pathname.getName())) {
                    return true;
                }
                return false;
            }
        }

        try {
            //得到包含CPU信息目录
            File dir = new File("/sys/devices/system/cpu/");
            //Filter to only list the devices we care about
            File[] files = dir.listFiles(new CpuFilter());
//            Log.e(TAG, "CPU Count: " + files.length);
            //返回核的数量(虚拟CPU设备)
            return files.length;
        } catch (Exception e) {
//            Log.e(TAG, "CPU Count: Failed.");
            e.printStackTrace();
            //默认返回1
            return 1;
        }
    }

}
