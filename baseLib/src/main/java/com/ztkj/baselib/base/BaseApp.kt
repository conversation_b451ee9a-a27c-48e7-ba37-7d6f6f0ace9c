package com.ztkj.baselib.base

import android.app.Application
import androidx.lifecycle.ProcessLifecycleOwner
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelStore
import androidx.lifecycle.ViewModelStoreOwner

/**
 * @CreateTime : 2022/10/12 10:22
 * <AUTHOR> AppOS
 * @Description : 提供一个很有用的功能--在Activity/fragment中获取Application级别的ViewModel
 */
open class BaseApp : Application(), ViewModelStoreOwner {

    private var mFactory: ViewModelProvider.Factory? = null


    override fun onCreate() {
        super.onCreate()
        //监听整个应用程序的生命周期
        ProcessLifecycleOwner.get().lifecycle.addObserver(ApplicationObserver())
    }

    /**
     * 获取一个全局的ViewModel
     */
    fun getAppViewModelProvider(): ViewModelProvider {
        return ViewModelProvider(this, this.getAppFactory())
    }

    private fun getAppFactory(): ViewModelProvider.Factory {
        if (mFactory == null) {
            mFactory = ViewModelProvider.AndroidViewModelFactory.getInstance(this)
        }
        return mFactory as ViewModelProvider.Factory
    }

    override val viewModelStore: ViewModelStore
        get() = ViewModelStore()


}