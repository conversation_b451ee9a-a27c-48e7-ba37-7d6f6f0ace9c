package com.ztkj.baselib.base.activity

import android.view.View
import androidx.databinding.ViewDataBinding
import com.ztkj.baselib.ext.inflateBindingWithGeneric
import com.ztkj.baselib.base.viewmodel.BaseViewModel

/**
 * @CreateTime : 2022/10/12 10:48
 * <AUTHOR> AppOS
 * @Description : 包含ViewModel 和Databind ViewModelActivity基类，把ViewModel 和Databind注入进来了
 * 需要使用Databind的清继承它
 */
abstract class BaseVmDbActivity<VM : BaseViewModel, DB : ViewDataBinding> : BaseVmActivity<VM>() {

    override fun layoutId() = 0

    lateinit var binding: DB

    /**
     * 创建DataBinding
     */
    override fun initDataBind(): View? {
        binding = inflateBindingWithGeneric(layoutInflater)
        return binding.root
    }
}