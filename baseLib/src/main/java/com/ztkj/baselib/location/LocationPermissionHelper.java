package com.ztkj.baselib.location;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Build;

import androidx.annotation.NonNull;
import androidx.fragment.app.FragmentActivity;

import com.permissionx.guolindev.PermissionX;

/**
 * @CreateTime : 2022/10/20 15:30
 * <AUTHOR> AppOS
 * @Description : 定位权限封装工具类
 */
public class LocationPermissionHelper {
    private LocationPermissionCallBack callBack;
    private Context context;

    /**
     * 定位权限获取成功回调
     */
    public interface LocationPermissionCallBack {
        void onSuccess();
    }

    /**
     * 定位权限组
     */
    private final String[] perms = new String[]{
            Manifest.permission.ACCESS_COARSE_LOCATION,
            Manifest.permission.ACCESS_FINE_LOCATION,
    };


    /**
     * 后台连续定位需要的权限
     * 1.Android 10 之前只有ACCESS_FINE_LOCATION和ACCESS_COARSE_LOCATION
     * 2.Android 10 新增加了后台定位权限：ACCESS_BACKGROUND_LOCATION，该权限对应始终允许；
     * 老的权限：ACCESS_FINE_LOCATION和ACCESS_COARSE_LOCATION代表仅前台使用允许；
     * 3.应用的targetSdkVersion<Q，谷歌提供了兼容性方案，只要应用申请了老的位置权限ACCESS_FINE_LOCATION或者ACCESS_COARSE_LOCATION，
     * 会默认请求ACCESS_BACKGROUND_LOCATION权限
     * 4.应用的TargetSdkVersion>=Q，如果应用必须要始终定位，可以只申请ACCESS_BACKGROUND_LOCATION即可
     *
     * @param context
     */
    public void requestBackground(@NonNull Context context, @NonNull LocationPermissionCallBack callBack) {
        this.callBack = callBack;
        this.context = context;
        callBack.onSuccess();
    }


}
