package com.ztkj.baselib.location;

import android.content.Context;
import android.util.Log;

import com.amap.api.location.AMapLocation;
import com.amap.api.location.AMapLocationClient;
import com.amap.api.location.AMapLocationClientOption;
import com.amap.api.location.AMapLocationListener;
import com.ztkj.baselib.base.Ktx;

/**
 * @CreateTime : 2022/10/20 15:36
 * <AUTHOR> AppOS
 * @Description :
 * 定位(这里使用高德定位)
 *  * 目前手机设备在长时间黑屏或锁屏时CPU会休眠，这导致定位SDK不能正常进行位置更新。
 *  * 若您有锁屏状态下获取位置的需求，您可以应用alarmManager实现1个可叫醒CPU的Timer，定时请求定位。
 */
public class LocationUtils {
    private static LocationUtils instance;

    public static LocationUtils getInstance() {
        if (instance == null) {
            synchronized (LocationUtils.class) {
                if (instance == null) {
                    instance = new LocationUtils();
                }
            }
        }
        return instance;
    }

    private AMapLocationClient mLocationClient;

    private LocationUtils() {
        try {
            /**
             * 在构造AMapLocationClient 之前必须进行合规检查
             */
            //设置包含隐私政策，并展示用户授权弹窗
            Context applicationContext = Ktx.app.getApplicationContext();

            AMapLocationClient.updatePrivacyShow(applicationContext, true, true);
            //设置是否同意用户授权政策
            AMapLocationClient.updatePrivacyAgree(applicationContext, true);

            mLocationClient = new AMapLocationClient(applicationContext);


            /**
             * 设置定位场景，目前支持三种场景（签到、出行、运动，默认无场景）
             */
            AMapLocationClientOption option = new AMapLocationClientOption();
            option.setLocationPurpose(AMapLocationClientOption.AMapLocationPurpose.Transport);
            mLocationClient.setLocationOption(option);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public interface CallBack {
        void onSuccess(LocationModel locationModel);
    }

    private CallBack callBack;

    private enum Type {
        SINGE,      //单次定位
        CONTINUE,   //连续定位
    }

    private Type curType;

    /**
     * 停止定位
     */
    public void stopLocation() {
        if (mLocationClient == null) {
            Log.e("123", "mLocationClient初始化异常");
            return;
        }
        mLocationClient.setLocationListener(null);
        mLocationClient.stopLocation();
        callBack = null;
        //清除保存的locationModel数据
        locationModel.longitude = 0;
        locationModel.latitude = 0;
        if (curType == Type.CONTINUE) {
            //停止定时任务
            AlarmManagerUtils.stop();
        }
    }

    /**
     * 单次定位获取当前位置
     *
     * @param callBack
     */
    public void getLocation(Context context, CallBack callBack) {
        if (mLocationClient == null) {
            Log.e("123", "mLocationClient初始化异常");
            return;
        }
        this.callBack = callBack;
        curType = Type.SINGE;
        mLocationClient.setLocationListener(mAMapLocationListener);
        mLocationClient.startLocation();
    }

    /**
     * 连续定位
     *
     * @param context
     * @param callBack
     */
    public void updateLocation(Context context, CallBack callBack) {
        if (mLocationClient == null) {
            Log.e("123", "mLocationClient初始化异常");
            return;
        }
        //先判断定位权限,在判断后台定位权限
        new LocationPermissionHelper().requestBackground(context, () -> {
            this.callBack = callBack;
            curType = Type.CONTINUE;
            mLocationClient.setLocationListener(mAMapLocationListener);
            mLocationClient.startLocation();
            //开启定时任务，防止CPU休眠
            AlarmManagerUtils.start();
        });
    }

    private final LocationModel locationModel = new LocationModel();
    private final AMapLocationListener mAMapLocationListener = new AMapLocationListener() {
        @Override
        public void onLocationChanged(AMapLocation amapLocation) {
            if (amapLocation.getErrorCode() == 0) {
                //排除相同的位置点位
                if (amapLocation.getLatitude() == locationModel.latitude
                        && amapLocation.getLongitude() == locationModel.longitude) {
                    return;
                }
                //可在其中解析amapLocation获取相应内容。
                locationModel.build(amapLocation);
                if (callBack != null) {
                    callBack.onSuccess(locationModel);
                }
                //单次定位，获取定位数据后直接停止
                if (curType == Type.SINGE) {
                    stopLocation();
                }
            } else {
                //定位失败时，可通过ErrCode（错误码）信息来确定失败的原因，errInfo是错误信息，详见错误码表。
                Log.e("LocationUtils", "location Error, ErrCode:"
                        + amapLocation.getErrorCode() + ", errInfo:"
                        + amapLocation.getErrorInfo());
            }
        }
    };
}
