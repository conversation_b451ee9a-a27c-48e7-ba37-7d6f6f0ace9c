package com.ztkj.baselib.network.manager

import com.ztkj.baselib.callback.livedata.event.EventLiveData

/**
 * @CreateTime : 2022/10/12 10:39
 * <AUTHOR> AppOS
 * @Description :网络变化管理者
 */
class NetworkStateManager private constructor() {

    val mNetworkStateCallback = EventLiveData<NetState>()

    companion object {
        val instance: NetworkStateManager by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            NetworkStateManager()
        }
    }

}