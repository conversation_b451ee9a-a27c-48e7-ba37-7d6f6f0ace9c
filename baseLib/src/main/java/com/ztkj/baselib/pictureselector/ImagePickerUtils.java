package com.ztkj.baselib.pictureselector;

import android.content.Context;

import com.luck.picture.lib.basic.PictureSelector;
import com.luck.picture.lib.config.SelectMimeType;
import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.interfaces.OnResultCallbackListener;

import java.util.ArrayList;
import java.util.List;

/**
 * 拍照/相册获取图片(如果需要上传文件的话，需要初始化UploadCallBack)
 */
public class ImagePickerUtils {

    /**
     * 文件上传接口
     */
    public interface UploadCallBack {
        //文件上传
        void upload(Context context, List<LocalMedia> list, CallBack callBack);
    }

    private static UploadCallBack uploadCallBack;

    private static int MAX_SELECT_NUM = 9;

    /**
     * 初始化文件上传接口
     *
     * @param callBack 文件上传接口
     */
    public static void initUploadCallBack(UploadCallBack callBack) {
        uploadCallBack = callBack;
    }


    public interface CallBack {
        void onPick(List<LocalMedia> list);
    }

    /**
     *
     * @param count 图片最大选择的数量
     */
    public static void injectMaxSelectNum(int count) {
        MAX_SELECT_NUM = count;
    }

    public static void pick(Context context,List<LocalMedia> selectedData, CallBack callBack) {
        PictureSelector.create(context)
                .openGallery(SelectMimeType.ofImage())
                .setCompressEngine(new ImageFileCompressEngine())
                .setMaxSelectNum(MAX_SELECT_NUM)
                .setSelectedData(selectedData)
                .setImageEngine(GlideEngine.createGlideEngine())
                .forResult((new OnResultCallbackListener<LocalMedia>() {
                    @Override
                    public void onResult(ArrayList<LocalMedia> result) {
                        // 结果回调
                        if (result.size() > 0) {
//                            List<LocalMedia> list = new ArrayList<>();
//                            for (LocalMedia media : result) {
//                                Log.e("123", "压缩::" + media.getCompressPath());
//                                Log.e("123", "原图::" + media.getPath());
//                                Log.e("123", "裁剪::" + media.getCutPath());
//                                Log.e("123", "是否开启原图::" + media.isOriginal());
//                                Log.e("123", "原图路径::" + media.getOriginalPath());
//                                list.add(media);
//                            }
                            if (uploadCallBack != null) {
                                //设置了文件上传接口,则先上传文件
                                uploadCallBack.upload(context, result, callBack);
                            } else {
                                //否则，直接返回本地路径
                                if (callBack != null) {
                                    callBack.onPick(result);
                                }
                            }
                        }
                    }

                    @Override
                    public void onCancel() {
                        // 取消
                    }
                }));
    }

}
