plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

android {
    compileSdk 32

    defaultConfig {
        minSdk 23
        targetSdk 32

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildFeatures {
        dataBinding = true
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }
    namespace 'com.ztkj.baselib'
}

dependencies {

    implementation 'androidx.core:core-ktx:1.7.0'
    api 'com.kunminx.archi:unpeek-livedata:4.4.1-beta1'
    //lifecycle
    api 'androidx.lifecycle:lifecycle-process:2.6.1'
    api 'androidx.lifecycle:lifecycle-extensions:2.2.0'
    api 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1'
    //ktx
    api 'androidx.activity:activity-ktx:1.6.0-alpha05'
    api "androidx.fragment:fragment-ktx:1.5.1"
    //navigation
    api 'androidx.navigation:navigation-fragment-ktx:2.3.5'
    api 'androidx.navigation:navigation-ui-ktx:2.3.5'
    //retrofit
    api "com.squareup.retrofit2:retrofit:2.9.0"
    api "com.squareup.retrofit2:converter-gson:2.9.0"
    api 'com.github.franmontiel:PersistentCookieJar:v1.0.1'
    //动态替换BaseUrl库 使用可参考 https://github.com/JessYanCoding/RetrofitUrlManager
    api 'me.jessyan:retrofit-url-manager:1.4.0'
    api 'com.google.code.gson:gson:2.10.1'
    //fastjson1 - 支持Android API 23+，排除fastjson2依赖
    api('com.alibaba:fastjson:1.2.83') {
        exclude group: 'com.alibaba.fastjson2'
    }

    //高德定位
    implementation files('libs/AMap_Location_V6.1.0_20220414.jar')
    //glide
    api 'com.github.bumptech.glide:glide:4.15.1'
    //权限
    api 'com.guolindev.permissionx:permissionx:1.7.1'

    // PictureSelector 基础 (必须)
    api 'io.github.lucksiege:pictureselector:v3.10.7'
    // 图片压缩 (按需引入)
    api 'io.github.lucksiege:compress:v3.10.7'
    // 图片裁剪 (按需引入)
    api 'io.github.lucksiege:ucrop:v3.10.7'
    //util
    api 'com.blankj:utilcodex:1.31.0'

}