package com.ztkj.imext.im;


import android.util.Log;

import com.ztkj.im.listener.IMSConnectStatusCallback;

/**
 * <p>@ProjectName:     NettyChat</p>
 * <p>@ClassName:       IMSConnectStatusListener.java</p>
 * <p>@PackageName:     com.ztkj.acq.chat.im</p>
 * <b>
 * <p>@Description:     类描述</p>
 * </b>
 * <p>@author:          <PERSON><PERSON><PERSON></p>
 * <p>@date:            2019/04/08 00:31</p>
 * <p>@email:           <EMAIL></p>
 */
public class IMSConnectStatusListener implements IMSConnectStatusCallback {
    private static final String TAG = "IMSConnectStatusListene";

    @Override
    public void onConnecting() {
        Log.e(TAG, "onConnecting: ");
    }

    @Override
    public void onConnected() {
        Log.e(TAG, "onConnected: " );
    }

    @Override
    public void onConnectFailed() {
        Log.e(TAG, "onConnectFailed: " );
    }
}
