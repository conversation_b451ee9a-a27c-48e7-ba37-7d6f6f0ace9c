package com.ztkj.imext.im;

import com.ztkj.imext.bean.AppMessage;
import com.ztkj.imext.bean.BaseMessage;
import com.ztkj.imext.bean.ContentMessage;

/**
 * <p>@ProjectName:     NettyChat</p>
 * <p>@ClassName:       IMessageProcessor.java</p>
 * <p>@PackageName:     com.ztkj.acq.chat.im</p>
 * <b>
 * <p>@Description:     消息处理器接口</p>
 * </b>
 * <p>@author:          <PERSON><PERSON><PERSON></p>
 * <p>@date:            2019/04/10 00:11</p>
 * <p>@email:           <EMAIL></p>
 */
public interface IMessageProcessor {

    void receiveMsg(AppMessage message);
    void sendMsg(AppMessage message);
    void sendMsg(ContentMessage message);
    void sendMsg(BaseMessage message);
}
