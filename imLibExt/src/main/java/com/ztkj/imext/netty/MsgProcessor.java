package com.ztkj.imext.netty;

import android.util.Log;

import com.ztkj.imext.utils.CThreadPoolExecutor;

/**
 * <p>@ProjectName:     NettyChat</p>
 * <p>@ClassName:       MessageProcessor.java</p>
 * <p>@PackageName:     com.ztkj.acq.chat.im</p>
 * <b>
 * <p>@Description:     消息处理器</p>
 * </b>
 * <p>@author:          <PERSON><PERSON><PERSON></p>
 * <p>@date:            2019/04/10 03:27</p>
 * <p>@email:           <EMAIL></p>
 */
//差分服务器数据
public class MsgProcessor implements IMsgProcessor {

    private static final String TAG = MsgProcessor.class.getSimpleName();

    private MsgProcessor() {

    }

    private static class MsgProcessorInstance {
        private static final IMsgProcessor INSTANCE = new MsgProcessor();
    }

    public static IMsgProcessor getInstance() {
        return MsgProcessorInstance.INSTANCE;
    }

    /**
     * 发送消息
     *
     * @param message
     */
    @Override
    public void sendMsg(final String message) {
        CThreadPoolExecutor.runInBackground(new Runnable() {

            @Override
            public void run() {
                boolean isActive = TcpClientBootstrap.getInstance().isActive();
                if (isActive) {
                    TcpClientBootstrap.getInstance().sendMessage(message);
                } else {
                    Log.e(TAG, "发送消息失败");
                }
            }
        });
    }

}
