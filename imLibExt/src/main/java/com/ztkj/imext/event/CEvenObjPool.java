package com.ztkj.imext.event;

/**
 * 事件对象池
 *
 * Created by <PERSON> on 2015/11/3.
 * ch<PERSON><PERSON><PERSON>@outlook.com
 */
public class CEvenObjPool extends ObjectPool<CEvent> {

    public CEvenObjPool(int capacity) {
        super(capacity);
    }

    @Override
    protected CEvent[] createObjPool(int capacity) {
        return new CEvent[capacity];
    }

    @Override
    protected CEvent createNewObj() {
        return new CEvent();
    }
}
