package com.ztkj.netty;

/**
 * <p>@ProjectName:     NettyChat</p>
 * <p>@ClassName:       IMSClientFactory.java</p>
 * <p>@PackageName:     com.freddy.im</p>
 * <b>
 * <p>@Description:     ims实例工厂方法</p>
 * </b>
 * <p>@author:          <PERSON><PERSON><PERSON></p>
 * <p>@date:            2019/03/31 20:54</p>
 * <p>@email:           chens<PERSON><PERSON>@outlook.com</p>
 */
public class TcpClientFactory {

    public static TcpClientInterface getTcpClient() {
        return TcpClient.getInstance();
    }
}
