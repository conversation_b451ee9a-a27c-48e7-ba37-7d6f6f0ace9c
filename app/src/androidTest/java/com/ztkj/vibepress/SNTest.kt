package com.ztkj.vibepress

import android.os.Build
import android.util.Log
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith

/**
 * @CreateTime : 2023/5/16 14:30
 * <AUTHOR> AppOS
 * @Description :
 */
@RunWith(AndroidJUnit4::class)
class SNTest {
    @Test
    fun getSerialNumber() {
        //android 6 ,9 getsn
        var serial: String? = null
        try {
            val c = Class.forName("android.os.SystemProperties")
            val get = c.getMethod("get", String::class.java)
            serial = get.invoke(c, "ro.serialno") as String
        } catch (e: Exception) {
            e.printStackTrace()
            Log.e("setSerialNumber", "获取设备序列号失败")
        }
        Log.e("ZJL", "++sdk+" + Build.VERSION.SDK_INT + "--sn--" + serial)
    }
}