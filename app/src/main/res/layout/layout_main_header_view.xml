<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="?attr/actionBarSize"
    android:background="@color/white"
    android:elevation="10dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/title"
            android:layout_width="280dp"
            android:layout_height="match_parent"
            android:layout_marginStart="8dp"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="5dp"
            android:background="@mipmap/ic_main_title_bg"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/serialNumber"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:text="S/N: 123455"
            android:textColor="#333333"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/title"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="S/N: 123455" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginEnd="20dp"
            app:layout_constraintEnd_toStartOf="@+id/setting"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_tensionValue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:drawablePadding="5dp"
                android:textColor="#333333"
                android:textSize="16sp"
                android:layout_marginEnd="10dp"
                app:drawableStartCompat="@drawable/ic_tension_status"
                tools:text="5" />

            <TextView
                android:id="@+id/tv_tensionStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:drawablePadding="5dp"
                android:textColor="#333333"
                android:textSize="16sp"
                android:layout_marginEnd="10dp"
                app:drawableStartCompat="@drawable/ic_tension_value"
                tools:text="5" />

            <TextView
                android:id="@+id/tv_satelliteNumber"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:drawablePadding="5dp"
                android:textColor="#333333"
                android:textSize="16sp"
                android:layout_marginEnd="10dp"
                app:drawableStartCompat="@drawable/ic_satellite_number"
                tools:text="5" />

            <TextView
                android:id="@+id/tv_gpsStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:drawablePadding="5dp"
                android:textColor="#333333"
                android:textSize="16sp"
                app:drawableStartCompat="@drawable/ic_gpsstatus"
                tools:text="5" />

        </LinearLayout>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/setting"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginEnd="24dp"
            android:padding="5dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_setting" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>