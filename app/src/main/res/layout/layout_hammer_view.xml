<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:clipChildren="false"
    android:background="@color/white"
    android:layout_height="wrap_content">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/xu"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginTop="6dp"
        android:layout_marginBottom="-1dp"
        app:layout_constraintTop_toTopOf="@id/main_car"
        android:layout_marginEnd="-9dp"
        android:scaleType="fitXY"
        app:layout_constraintBottom_toBottomOf="@+id/main_car"
        app:layout_constraintEnd_toEndOf="@+id/main_car"
        app:srcCompat="@drawable/xu_t" />


    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/chui"
        app:layout_constraintBottom_toBottomOf="@+id/main_car"
        app:layout_constraintEnd_toEndOf="@+id/main_car"
        android:layout_width="30dp"
        android:layout_marginEnd="-13dp"
        android:layout_height="258dp"
        android:scaleType="fitXY"
        android:layerType="hardware"
        android:src="@drawable/hammer" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/main_car"
        android:layout_width="150dp"
        android:layout_height="260dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:srcCompat="@drawable/maincar" />

    <com.ztkj.vibepress.app.widget.customview.ElevationView
        android:id="@+id/elevationView"
        android:layout_width="wrap_content"
        android:layout_marginStart="10dp"
        app:layout_constraintStart_toEndOf="@id/main_car"
        app:layout_constraintBottom_toBottomOf="@id/main_car"
        android:layout_height="wrap_content" />


</androidx.constraintlayout.widget.ConstraintLayout>