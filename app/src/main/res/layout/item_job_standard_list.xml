<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    android:background="#2A2FC1FF"
    android:orientation="horizontal"
    android:paddingStart="16dp"
    android:paddingEnd="16dp">


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/name"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="16sp" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/type"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center"
        android:text="@string/type"
        android:textColor="@color/white"
        android:textSize="16sp" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/liftingHeight"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="16sp" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/liftingTimes"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="16sp" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="3"
        android:orientation="horizontal"
        android:paddingStart="10dp"
        android:paddingTop="6dp"
        android:paddingEnd="5dp"
        android:paddingBottom="6dp">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/setToCurrent"
            style="@style/JobStandardListOperationTextView"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="10dp"
            android:layout_weight="2"
            android:text="@string/set_at_current" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/modify"
            style="@style/JobStandardListOperationTextView"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="10dp"
            android:layout_weight="1"
            android:text="@string/modify" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/delete"
            style="@style/JobStandardListOperationTextView"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="10dp"
            android:layout_weight="1"
            android:background="@drawable/shape_job_list_bordered_textview2"
            android:text="@string/delete"
            android:textColor="#FF5B5B" />
    </LinearLayout>


</LinearLayout>