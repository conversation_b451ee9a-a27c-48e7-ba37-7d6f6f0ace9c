<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".ui.fragment.home.HomeFragment">

        <androidx.fragment.app.FragmentContainerView
            android:id="@+id/fragment_animation"
            android:name="com.ztkj.vibepress.ui.fragment.home.CounterFragment"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            app:layout_constraintEnd_toStartOf="@+id/guideline"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:layout="@layout/fragment_counter" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline"
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.4" />

        <androidx.fragment.app.FragmentContainerView
            android:id="@+id/fragment_map"
            android:name="com.ztkj.vibepress.ui.fragment.home.MapFragment"
            android:layout_width="0dp"
            tools:layout="@layout/fragment_map"
            android:layout_height="match_parent"
            app:layout_constraintStart_toEndOf="@+id/guideline"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <com.ztkj.vibepress.app.widget.customview.MainHeaderView
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>