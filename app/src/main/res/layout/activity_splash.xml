<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="click"
            type="com.ztkj.vibepress.ui.activity.SplashActivity.ProxyClick" />
    </data>

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:id="@+id/ivSplashPicture"
        android:scaleType="fitXY"
        android:src="@drawable/splash_launcher"
        android:contentDescription="@string/app_name" />
</layout>