<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="click"
            type="com.ztkj.vibepress.ui.fragment.setting.BoardSettingFragment.ProxyClick" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".ui.fragment.setting.BoardSettingFragment">


        <com.ztkj.vibepress.app.widget.customview.VibeHeaderView
            android:id="@+id/toolbar"
            app:centerTitle="@string/board_setting"
            app:showRightBtn="false"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <RadioGroup
            android:orientation="horizontal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/toolbar"
            android:id="@+id/radioGroup"
            android:checkedButton="@+id/radio_button_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <com.google.android.material.radiobutton.MaterialRadioButton
                android:id="@+id/radio_button_1"
                android:layout_width="match_parent"
                android:checked="true"
                android:textSize="18sp"
                android:textColor="@color/white"
                android:layout_height="match_parent"
                android:text="@string/gnss" />

            <com.google.android.material.radiobutton.MaterialRadioButton
                android:layout_marginStart="40dp"
                android:textColor="@color/white"
                android:id="@+id/radio_button_2"
                android:textSize="18sp"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:text="@string/counter_sensor" />
        </RadioGroup>

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/etCommand"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:background="@drawable/item_setting_border2"
            app:layout_constraintTop_toBottomOf="@id/radioGroup"
            android:layout_marginTop="24dp"
            android:layout_marginStart="66dp"
            android:gravity="center_vertical"
            android:textColorHint="@color/white"
            android:paddingStart="10dp"
            android:paddingEnd="0dp"
            android:textColor="@color/white"
            android:layout_marginEnd="66dp"
            android:hint="@string/board_edittext_tips"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <LinearLayout
            android:layout_marginTop="10dp"
            android:id="@+id/ll_function"
            app:layout_constraintTop_toBottomOf="@id/etCommand"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:orientation="horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <com.google.android.material.button.MaterialButton
                app:cornerRadius="6dp"
                android:onClick="@{ () -> click.clearReceive()}"
                android:id="@+id/clearReceive"
                android:text="@string/clear_receive"
                style="@style/BoardSettingMaterialButtonStyle" />

            <com.google.android.material.button.MaterialButton
                style="@style/BoardSettingMaterialButtonStyle"
                app:cornerRadius="6dp"
                android:id="@+id/clearSend"
                android:onClick="@{ () -> click.clearSend()}"
                android:text="@string/clear_send" />

            <com.google.android.material.button.MaterialButton
                style="@style/BoardSettingMaterialButtonStyle"
                app:cornerRadius="6dp"
                android:onClick="@{ () -> click.sendCommand()}"
                android:id="@+id/btSendCommand"
                android:text="@string/send_command" />

            <com.google.android.material.button.MaterialButton
                app:cornerRadius="6dp"
                style="@style/BoardSettingMaterialButtonStyle"
                android:onClick="@{ () -> click.autoLoad()}"
                android:id="@+id/btAutoLoad"
                android:text="@string/autoload" />

        </LinearLayout>


        <FrameLayout
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginStart="66dp"
            android:id="@+id/tvResult"
            android:layout_marginEnd="66dp"
            android:background="@drawable/item_setting_border2"
            app:layout_constraintTop_toBottomOf="@id/ll_function"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_width="match_parent"
            android:layout_marginTop="10dp"
            android:padding="3dp"
            android:layout_marginBottom="10dp"
            android:layout_height="0dp">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvData"
                tools:listitem="@layout/item_simple_recyclerview_text"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
        </FrameLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>


</layout>