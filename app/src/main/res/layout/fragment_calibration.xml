<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="click"
            type="com.ztkj.vibepress.ui.fragment.setting.CalibrationFragment.ProxyClick" />

        <variable
            name="vm"
            type="com.ztkj.vibepress.viewmodel.state.CalibrationViewModel" />

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:context=".ui.fragment.setting.CalibrationFragment">

        <com.ztkj.vibepress.app.widget.customview.VibeHeaderView
            android:id="@+id/toolBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:centerTitle="@string/calibration"
            app:showRightBtn="true" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="26dp"
            android:layout_marginTop="10dp"
            android:text="@string/operation_instructions"
            android:textColor="@color/white"
            android:textSize="16sp" />

        <LinearLayout
            android:layout_marginTop="5dp"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="200dp"
                android:layout_height="48dp"
                android:gravity="center_vertical|end"
                android:text="张力状态:"
                android:textColor="@color/white"
                android:textSize="18sp" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tensionValue"
                android:layout_width="190dp"
                android:layout_height="48dp"
                android:layout_marginStart="5dp"
                android:background="@drawable/shape_setting_item_border"
                android:maxLines="1"
                android:text="@{vm.tensionValue}"
                android:paddingStart="5dp"
                android:paddingEnd="0dp"
                android:textColor="@color/white"
                android:textColorHint="@color/white"
                android:textSize="18sp" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/saveTensionValue"
                android:visibility="invisible"
                android:layout_width="96dp"
                android:layout_height="44dp"
                android:layout_marginStart="10dp"
                android:background="@drawable/item_setting_border"
                android:gravity="center"
                android:onClick="@{() -> click.saveTensionValue()}"
                android:text="保存"
                android:textColor="#2FF2FF" />


        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="200dp"
                android:layout_height="48dp"
                android:gravity="center_vertical|end"
                android:text="@string/current_encoder_value"
                android:textColor="@color/white"
                android:textSize="18sp" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/currentCount"
                android:layout_width="190dp"
                android:layout_height="48dp"
                android:layout_marginStart="5dp"
                android:background="@mipmap/ic_setting_diagnosis"
                android:gravity="center_vertical|start"
                android:paddingStart="5dp"
                android:text="@{vm.sensorDataValue}"
                android:textColor="@color/white"
                android:textSize="18sp" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/clearSend"
                android:layout_width="96dp"
                android:visibility="visible"
                android:layout_height="44dp"
                android:layout_marginStart="10dp"
                android:background="@drawable/item_setting_border"
                android:gravity="center"
                android:onClick="@{() -> click.clear()}"
                android:text="清零"
                android:textColor="#2FF2FF" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginTop="20dp"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="200dp"
                android:layout_height="48dp"
                android:gravity="center_vertical|end"
                android:text="提升高度:"
                android:textColor="@color/white"
                android:textSize="18sp" />

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/edittext"
                android:layout_width="190dp"
                android:layout_height="48dp"
                android:layout_marginStart="5dp"
                android:background="@drawable/shape_setting_item_border"
                android:hint="不低于3米"
                android:imeOptions="actionNext"
                android:inputType="numberDecimal"
                android:maxLines="1"
                android:paddingStart="5dp"
                android:paddingEnd="0dp"
                android:textColor="@color/white"
                android:textColorHint="@color/white"
                android:textSize="18sp" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/ok"
                android:visibility="invisible"
                android:layout_width="96dp"
                android:layout_height="44dp"
                android:layout_marginStart="10dp"
                android:background="@drawable/item_setting_border"
                android:gravity="center"
                android:onClick="@{ () -> click.done()}"
                android:text="确定"
                android:textColor="#2FF2FF" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginTop="20dp"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="200dp"
                android:layout_height="48dp"
                android:gravity="center_vertical|end"
                android:text="当前锤高系数:"
                android:textColor="@color/white"
                android:textSize="18sp" />

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/hammerHeightRatio"
                android:layout_width="190dp"
                android:layout_height="48dp"
                android:enabled="false"
                android:layout_marginStart="5dp"
                android:background="@drawable/shape_setting_item_border"
                android:inputType="numberDecimal"
                android:maxLines="1"
                android:paddingStart="5dp"
                android:paddingEnd="0dp"
                android:textColor="@color/white"
                android:textColorHint="@color/white"
                android:textSize="18sp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/calibrate"
                style="@style/BoardSettingMaterialButtonStyle"
                android:layout_gravity="center_horizontal"
                android:onClick="@{ () -> click.calibrate()}"
                android:text="校准"
                app:cornerRadius="6dp" />
        </LinearLayout>
    </LinearLayout>
</layout>