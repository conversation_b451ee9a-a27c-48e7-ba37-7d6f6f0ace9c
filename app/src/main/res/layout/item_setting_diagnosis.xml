<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:background="@color/transparent"
    android:minHeight="40dp"
    android:layout_height="wrap_content">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/title"
        android:layout_width="100dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:layout_marginStart="16dp"
        android:paddingEnd="5dp"
        android:paddingStart="0dp"
        android:layout_gravity="center_vertical|end"
        android:text="@string/gpgga_data"
        android:textSize="15sp" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/content"
        android:layout_weight="1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="24dp"
        android:gravity="center|start"
        android:layout_gravity="center|start"
        android:textColor="@color/white"
        android:paddingStart="5dp"
        android:paddingEnd="5dp"
        android:background="@mipmap/ic_setting_diagnosis" />


</LinearLayout>