<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <include layout="@layout/include_toolbar" android:id="@+id/include_layout" />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:fillViewport="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:contentDescription="@null"
                    android:src="@drawable/customactivityoncrash_error_image" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:layout_marginStart="12dp"
                    android:layout_marginEnd="12dp"
                    android:gravity="center"
                    android:lineSpacingMultiplier="1.5"
                    android:text="@string/error_occurred"
                    android:textSize="15dp"
                    android:textStyle="bold" />

                <Button
                    android:id="@+id/errorRestart"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:text="重新启动"
                    android:textSize="15dp" />

                <Button
                    android:visibility="invisible"
                    android:id="@+id/errorSendError"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:text="发送日志"
                    android:textSize="15dp" />
            </LinearLayout>
        </ScrollView>
    </LinearLayout>
</layout>