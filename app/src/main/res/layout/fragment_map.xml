<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="vm"
            type="com.ztkj.vibepress.viewmodel.state.MapStateViewModel" />

        <variable
            name="click"
            type="com.ztkj.vibepress.ui.fragment.home.MapFragment.ProxyClick" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".ui.fragment.home.MapFragment">


        <com.esri.arcgisruntime.mapping.view.MapView
            android:id="@+id/mapView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!--        <com.google.android.material.imageview.ShapeableImageView
                    android:id="@+id/setting"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="16dp"
                    app:shapeAppearance="@style/RoundedStyle"
                    android:background="@color/colorAccent"
                    android:onClick="@{ () -> click.setting()}"
                    android:src="@mipmap/setting"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />-->

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/legend_device"
            android:layout_width="57dp"
            android:layout_height="66dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:srcCompat="@mipmap/legend_design" />

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/ic_map_zoom_in"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginTop="66dp"
            android:layout_marginEnd="16dp"
            android:onClick="@{() -> click.zoomIn()}"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_width="wrap_content"
            app:srcCompat="@mipmap/ic_zoom_in"
            android:layout_height="wrap_content" />

        <com.google.android.material.imageview.ShapeableImageView
            app:layout_constraintTop_toBottomOf="@id/ic_map_zoom_in"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:onClick="@{() -> click.zoomOut()}"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_width="wrap_content"
            app:srcCompat="@mipmap/ic_zoom_out"
            android:layout_height="wrap_content" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>