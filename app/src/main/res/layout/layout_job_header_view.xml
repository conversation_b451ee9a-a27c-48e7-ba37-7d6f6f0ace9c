<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#332FF2FF"
    android:orientation="horizontal"
    android:paddingStart="16dp"
    android:paddingEnd="16dp">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/name"
        style="@style/JobStandListHeaderTextView"
        android:text="@string/name" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/type"
        style="@style/JobStandListHeaderTextView"
        android:text="@string/type" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/liftingHeight"
        style="@style/JobStandListHeaderTextView"
        android:text="@string/requireLiftingHeight" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/liftingTimes"
        style="@style/JobStandListHeaderTextView"
        android:text="@string/liftingTimes" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/operation"
        style="@style/JobStandListHeaderTextView"
        android:layout_weight="3"
        android:text="@string/operation" />


</LinearLayout>