<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/title"
        tools:text="标题:"
        android:paddingEnd="5dp"
        android:maxLines="1"
        android:layout_gravity="end|center_vertical"
        android:paddingStart="0dp"
        android:gravity="end|center_vertical"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:layout_width="120dp"
        android:layout_height="wrap_content" />

    <com.skydoves.powerspinner.PowerSpinnerView
        android:id="@+id/spinner"
        app:spinner_arrow_gravity="end"
        app:spinner_arrow_padding="8dp"
        app:spinner_divider_show="false"
        app:spinner_item_height="46dp"
        android:textColorHint="@color/white"
        app:spinner_popup_animation="normal"
        app:spinner_popup_background="#212121"
        app:spinner_popup_elevation="14dp"
        app:spinner_selected_item_background="@drawable/selected_item_background"
        tools:ignore="HardcodedText,UnusedAttribute"
        style="@style/CustomSpinnerStyle" />
</LinearLayout>