<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".ui.fragment.setting.SettingFragment">

        <com.ztkj.vibepress.app.widget.customview.VibeHeaderView
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:centerTitle="@string/setting" />


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvData"
            android:layout_marginTop="10dp"
            app:layoutManager="com.chad.library.adapter.base.layoutmanager.QuickGridLayoutManager"
            app:spanCount="4"
            tools:listitem="@layout/item_main_setting"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </LinearLayout>
</layout>