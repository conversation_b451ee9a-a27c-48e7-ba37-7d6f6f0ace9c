<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="vm"
            type="com.ztkj.vibepress.viewmodel.state.LoginViewModel" />

        <variable
            name="click"
            type="com.ztkj.vibepress.ui.fragment.login.OnlineLoginFragment.ProxyClick" />

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:context=".ui.fragment.login.OnlineLoginFragment">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/etDataDockingServer"
            style="@style/NetParamBorderTextView"
            android:layout_width="match_parent"
            android:layout_marginTop="@dimen/netParamSettingMarginTop"
            android:gravity="center_vertical|start"
            android:text="夯机平台"
            app:layout_constraintEnd_toEndOf="@id/centerVerticalLine"
            app:layout_constraintTop_toBottomOf="@id/dataDockingTitle" />

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/username"
            style="@style/LoginEdittextStyle"
            android:hint="@string/serial_number_prompt"
            android:text="@={vm.username}" />

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/password"
            style="@style/LoginEdittextStyle"
            android:hint="@string/password_prompt"
            android:inputType="textPassword"
            android:text="@={vm.password}" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/login"
            style="@style/BoardSettingMaterialButtonStyle"
            android:layout_gravity="center"
            android:layout_marginTop="10dp"
            android:onClick="@{ () -> click.login()}"
            android:paddingStart="30dp"
            android:paddingEnd="30dp"
            android:text="@string/login"
            app:cornerRadius="6dp" />
    </LinearLayout>
</layout>