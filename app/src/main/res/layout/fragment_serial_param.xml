<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="config"
            type="com.ztkj.vibepress.data.model.bean.SerialConfigBean" />

        <variable
            name="click"
            type="com.ztkj.vibepress.ui.fragment.setting.SerialParamFragment.ProxyClick" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".ui.fragment.setting.SerialParamFragment">


        <com.ztkj.vibepress.app.widget.customview.VibeHeaderView
            android:id="@+id/toolbar"
            app:centerTitle="@string/serial_param_setting"
            app:showRightBtn="true"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/centerVertical"
            app:layout_constraintGuide_percent="0.5"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical" />

        <TextView
            android:id="@+id/rtkDesc"
            style="@style/SerialPortTextView"
            android:layout_marginTop="90dp"
            android:text="@string/rtkDescription"
            app:layout_constraintEnd_toEndOf="@id/centerVertical"
            app:layout_constraintTop_toBottomOf="@id/toolbar" />

        <TextView
            android:id="@+id/rtkLocation"
            android:text="@={config.rtkPath}"
            android:onClick="@{() -> click.rtkPath()}"
            app:layout_constraintBaseline_toBaselineOf="@id/rtkDesc"
            app:layout_constraintStart_toEndOf="@id/rtkDesc"
            style="@style/SerialPortBorderedTextView" />

        <TextView
            android:id="@+id/rtkBaudrate"
            android:text="@={config.rtkBaudrate}"
            android:onClick="@{() -> click.rtkBaudrate()}"
            app:layout_constraintStart_toStartOf="@id/centerVertical"
            app:layout_constraintTop_toBottomOf="@id/rtkLocation"
            style="@style/SerialPortBorderedTextView" />

        <TextView
            android:id="@+id/baudrateDesc"
            android:text="@string/serial_port_baudrate_desc"
            app:layout_constraintEnd_toEndOf="@id/rtkDesc"
            app:layout_constraintBaseline_toBaselineOf="@id/rtkBaudrate"
            style="@style/SerialPortTextView" />

        <TextView
            android:id="@+id/sensorPath"
            android:text="@={config.counterPath}"
            android:onClick="@{() -> click.sensorPath()}"
            app:layout_constraintStart_toStartOf="@id/centerVertical"
            app:layout_constraintTop_toBottomOf="@id/rtkBaudrate"
            style="@style/SerialPortBorderedTextView" />

        <TextView
            android:id="@+id/sensorPathDesc"
            android:text="@string/sensor_desc"
            app:layout_constraintEnd_toEndOf="@id/rtkDesc"
            app:layout_constraintBaseline_toBaselineOf="@id/sensorPath"
            style="@style/SerialPortTextView" />

        <TextView
            android:id="@+id/sensorBaudrate"
            android:text="@={config.counterBaudrate}"
            android:onClick="@{() -> click.sensorBaudrate()}"
            app:layout_constraintStart_toStartOf="@id/centerVertical"
            app:layout_constraintTop_toBottomOf="@id/sensorPath"
            style="@style/SerialPortBorderedTextView" />

        <TextView
            android:id="@+id/sensorBaudrateDesc"
            android:text="@string/sensor_baudrate_desc"
            app:layout_constraintEnd_toEndOf="@id/rtkDesc"
            app:layout_constraintBaseline_toBaselineOf="@id/sensorBaudrate"
            style="@style/SerialPortTextView" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>