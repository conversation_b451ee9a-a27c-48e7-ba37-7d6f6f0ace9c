<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll_error"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/loading_errorimg"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:src="@drawable/load_error" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="数据获取失败"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="12dp"
        android:textColor="#333333"
        android:textSize="14dp" />

    <TextView
        android:id="@+id/error_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="12dp"
        android:textSize="12dp"
        android:text="请检查网络后点击重新加载" />
</LinearLayout>
