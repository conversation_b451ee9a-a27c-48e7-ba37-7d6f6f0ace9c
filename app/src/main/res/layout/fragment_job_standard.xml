<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".ui.fragment.setting.JobStandardFragment">

        <com.ztkj.vibepress.app.widget.customview.VibeHeaderView
            android:id="@+id/toolBar"
            app:showRightBtn="true"
            app:centerTitle="@string/deviceAttribute"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvData"
            android:layout_marginTop="20dp"
            app:layoutManager="com.chad.library.adapter.base.layoutmanager.QuickGridLayoutManager"
            app:spanCount="2"
            tools:listitem="@layout/item_setting_edittext"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />


    </LinearLayout>
</layout>