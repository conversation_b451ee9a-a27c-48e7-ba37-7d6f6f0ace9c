<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />


    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />
    <!-- 自定义Dialog主题 考虑到好像就一个dialog，就没集成BottomSheet了 -->
    <style name="BottomDialogStyle" parent="@android:style/Theme.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/white</item>
        <item name="android:windowAnimationStyle">@style/BottomDialogAnimation</item>
    </style>
    <!-- 自定义Dialog动画 -->
    <style name="BottomDialogAnimation">
        <item name="android:windowEnterAnimation">@anim/push_bottom_in</item>
        <item name="android:windowExitAnimation">@anim/push_bottom_out</item>
    </style>

    <!--ShapeableImageView 圆角-->
    <style name="RoundedStyle">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">10dp</item>
    </style>

    <!--ShapeableImageView 圆 -->
    <style name="CircleStyle">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>

    <!--    设置页面Item TextView-->
    <style name="SettingTextView">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:drawablePadding">16dp</item>
        <item name="android:foreground">?android:attr/selectableItemBackground</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:padding">10dp</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">14sp</item>
        <item name="drawableEndCompat">@mipmap/ic_arrow_right</item>
    </style>

    <style name="BackTextView">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">?attr/actionBarSize</item>
        <item name="android:drawablePadding">16dp</item>
        <item name="android:foreground">?android:attr/selectableItemBackground</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:padding">10dp</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
    </style>

    <!--    边框带下拉选择的TextView-->
    <style name="BorderedTextView">
        <item name="android:background">@drawable/shape_bordered_textview</item>
        <item name="drawableEndCompat">@mipmap/ic_arrow_down</item>
        <item name="android:drawablePadding">16dp</item>
        <item name="android:padding">10dp</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="SerialPortBorderedTextView" parent="BorderedTextView">
        <item name="android:layout_width">200dp</item>
        <item name="android:layout_marginTop">20dp</item>
        <item name="android:layout_marginStart">5dp</item>
        <item name="android:layout_height">40dp</item>
    </style>

    <style name="SerialPortTextView">
        <item name="android:textSize">18sp</item>
        <item name="android:gravity">end</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
    </style>

    <!--    任务管理-->
    <style name="TaskQueryTextView">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:gravity">start|center_vertical</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:background">@drawable/shape_bordered_textview2</item>
        <item name="android:drawablePadding">20dp</item>
        <item name="android:foreground">?android:attr/selectableItemBackground</item>
        <item name="drawableEndCompat">@mipmap/ic_arrow_down</item>
    </style>

    <!--    达标统计-->
    <style name="ReachToStandardTextView">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:gravity">center</item>
        <item name="android:drawablePadding">20dp</item>
        <item name="android:foreground">?android:attr/selectableItemBackground</item>
        <item name="drawableEndCompat">@mipmap/ic_arrow_right</item>
    </style>

    <!--    首页工具栏ToolTextView-->
    <style name="HomeToolsTextView">
        <item name="android:foreground">?android:attr/selectableItemBackground</item>
        <item name="android:layout_width">48dp</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">12sp</item>
    </style>

    <!--    网络参数设置界面-->
    <style name="NetParamSettingTitleTextView">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/color_2FF2FF</item>
        <item name="android:textSize">18sp</item>
        <item name="android:drawablePadding">10dp</item>
    </style>

    <style name="NetParamSettingDescTextView">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:gravity">end</item>
        <item name="android:textSize">16sp</item>
        <item name="android:layout_marginEnd">5dp</item>
    </style>

    <style name="NetParamSettingNormalEditText">
        <item name="android:layout_width">200dp</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:hint">请输入...</item>
        <item name="android:padding">5dp</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textColorHint">@color/white</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="NetParamSettingIPEditText" parent="NetParamSettingNormalEditText">
        <item name="android:inputType">phone</item>
        <item name="android:digits">0123456789.</item>
        <item name="android:background">@drawable/shape_bordered_textview2</item>
    </style>

    <style name="NetParamSettingAlphaEditText" parent="NetParamSettingNormalEditText">
        <item name="android:inputType">number</item>
        <item name="android:background">@color/white_10alpha</item>
    </style>

    <style name="NetParamBorderTextView" parent="BorderedTextView">
        <item name="android:background">@drawable/shape_bordered_textview2</item>
        <item name="android:layout_width">200dp</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:hint">请输入...</item>
        <item name="android:padding">5dp</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textColorHint">@color/white</item>
        <item name="android:textSize">18sp</item>
    </style>

    <style name="MainDeviceAttributeTextView">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">#333333</item>
        <item name="android:layout_marginTop">5dp</item>
    </style>

    <style name="MainDeviceFunctionTextView">
        <item name="android:background">@drawable/shape_main_function_textview</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:paddingStart">5dp</item>
        <item name="android:paddingEnd">5dp</item>
        <item name="android:layout_height">45dp</item>
        <item name="android:layout_marginEnd">16dp</item>
        <item name="android:layout_marginBottom">16dp</item>
        <item name="android:textSize">20sp</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="JobStandListHeaderTextView">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">50dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="JobStandardListOperationTextView">
        <item name="android:textColor">#2FF2FF</item>
        <item name="android:textSize">16sp</item>
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/shape_job_list_bordered_textview</item>
    </style>

    <style name="CustomSpinnerStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:background">@drawable/shape_bordered_textview2</item>
        <item name="android:foreground">?attr/selectableItemBackground</item>
        <item name="android:padding">10dp</item>
        <item name="android:textColor">@color/white</item>
    </style>


    <style name="MyTextInputLayoutStyle" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">@color/red</item>
        <item name="hintTextColor">@color/white</item>
        <item name="android:background">@drawable/item_setting_border2</item>
        <item name="android:textColorHint">@color/red</item>
    </style>

    <style name="BoardSettingMaterialButtonStyle">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:textColor">#2FF2FF</item>
        <item name="android:backgroundTint">#1e6981</item>
        <item name="android:layout_marginStart">20dp</item>
    </style>

    <style name="LoginEdittextStyle">
        <item name="android:textColorHint">#515F75</item>
        <item name="android:maxLines">1</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:textSize">18sp</item>
        <item name="android:paddingStart">5dp</item>
        <item name="android:paddingEnd">5dp</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:layout_marginTop">10dp</item>
        <item name="android:background">@drawable/shape_bordered_textview2</item>
    </style>

    <style name="MultiSettingCellTextViewStyle">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:gravity">center_vertical|end</item>
        <item name="android:textSize">16sp</item>
        <item name="android:layout_weight">2</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="MultiSettingCellEditTextStyle">
        <item name="android:background">@drawable/shape_bordered_textview2</item>
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_weight">3</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:hint">请输入...</item>
        <item name="android:textColorHint">@color/white</item>
    </style>

    <style name="MultiSettingDropDownStyle">
        <item name="android:background">@drawable/shape_bordered_textview2</item>
        <item name="drawableEndCompat">@mipmap/ic_arrow_down</item>
        <item name="android:drawablePadding">16dp</item>
        <item name="android:padding">10dp</item>
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_weight">3</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textColorHint">@color/white</item>
    </style>

    <style name="MultiSettingCellTitleStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textSize">18sp</item>
        <item name="android:textColor">#1BCAD6</item>
        <item name="android:drawablePadding">10dp</item>
        <item name="android:paddingTop">3dp</item>
        <item name="android:paddingBottom">3dp</item>
    </style>

    <style name="MultiSettingTableLayoutStyle">
        <item name="android:layout_marginStart">10dp</item>
        <item name="android:layout_marginEnd">10dp</item>
        <item name="android:layout_marginTop">10dp</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@drawable/shape_bordered_textview2</item>
        <item name="android:stretchColumns">1</item>
    </style>

    <style name="MultiSettingTableRowStyle">
        <item name="android:layout_marginStart">30dp</item>
        <item name="android:layout_marginEnd">30dp</item>
        <item name="android:layout_marginTop">5dp</item>
    </style>


</resources>