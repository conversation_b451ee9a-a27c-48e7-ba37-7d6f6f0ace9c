<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph"
    app:startDestination="@id/loginFragment">

    <fragment
        android:id="@+id/loginFragment"
        android:name="com.ztkj.vibepress.ui.fragment.login.LoginFragment"
        android:label="fragment_login"
        tools:layout="@layout/fragment_login">
        <action
            android:id="@+id/action_loginFragment_to_homeFragment"
            app:destination="@id/homeFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
    </fragment>

    <fragment
        android:id="@+id/homeFragment"
        android:name="com.ztkj.vibepress.ui.fragment.home.HomeFragment"
        android:label="HomeFragment"
        tools:layout="@layout/fragment_home" />
    <fragment
        android:id="@+id/settingFragment"
        android:name="com.ztkj.vibepress.ui.fragment.setting.SettingFragment"
        android:label="SettingFragment"
        tools:layout="@layout/fragment_setting">
        <action
            android:id="@+id/action_settingFragment_to_diagnosisFragment"
            app:destination="@id/diagnosisFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
        <action
            android:id="@+id/action_settingFragment_to_serialParamFragment"
            app:destination="@id/serialParamFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
        <action
            android:id="@+id/action_settingFragment_to_aboutFragment"
            app:destination="@id/aboutFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
        <action
            android:id="@+id/action_settingFragment_to_deviceAttributeFragment"
            app:destination="@id/deviceAttributeFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
        <action
            android:id="@+id/action_settingFragment_to_netParamFragment"
            app:destination="@id/netParamFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
        <action
            android:id="@+id/action_settingFragment_to_jobStandardListFragment"
            app:destination="@id/jobStandardListFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
        <action
            android:id="@+id/action_settingFragment_to_boardSettingFragment"
            app:destination="@id/boardSettingFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
        <action
            android:id="@+id/action_settingFragment_to_calibrationFragment"
            app:destination="@id/calibrationFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
    </fragment>
    <action
        android:id="@+id/action_global_settingFragment"
        app:destination="@id/settingFragment"
        app:enterAnim="@anim/slide_in_right"
        app:exitAnim="@anim/slide_out_left"
        app:popEnterAnim="@anim/slide_in_left"
        app:popExitAnim="@anim/slide_out_right" />
    <fragment
        android:id="@+id/diagnosisFragment"
        android:name="com.ztkj.vibepress.ui.fragment.setting.DiagnosisFragment"
        android:label="DiagnosisFragment"
        tools:layout="@layout/fragment_diagnosis" />
    <fragment
        android:id="@+id/serialParamFragment"
        android:name="com.ztkj.vibepress.ui.fragment.setting.SerialParamFragment"
        android:label="SerialParamFragment"
        tools:layout="@layout/fragment_serial_param" />
    <fragment
        android:id="@+id/aboutFragment"
        android:name="com.ztkj.vibepress.ui.fragment.setting.AboutFragment"
        android:label="AboutFragment"
        tools:layout="@layout/fragment_about">
        <action
            android:id="@+id/action_aboutFragment_to_advancedSettingFragment"
            app:destination="@id/advancedSettingFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
        <action
            android:id="@+id/action_aboutFragment_to_multiSettingFragment"
            app:destination="@id/multiSettingFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
    </fragment>
    <fragment
        android:id="@+id/deviceAttributeFragment"
        android:name="com.ztkj.vibepress.ui.fragment.setting.DeviceAttributeFragment"
        android:label="DeviceAttributeFragment"
        tools:layout="@layout/fragment_device_attribute" />
    <fragment
        android:id="@+id/netParamFragment"
        android:name="com.ztkj.vibepress.ui.fragment.setting.NetParamFragment"
        android:label="NetParamFragment"
        tools:layout="@layout/fragment_net_param" />
    <fragment
        android:id="@+id/jobStandardFragment"
        android:name="com.ztkj.vibepress.ui.fragment.setting.JobStandardFragment"
        android:label="JobStandardFragment"
        tools:layout="@layout/fragment_job_standard" />
    <fragment
        android:id="@+id/jobStandardListFragment"
        android:name="com.ztkj.vibepress.ui.fragment.setting.JobStandardListFragment"
        android:label="JobStandardListFragment"
        tools:layout="@layout/fragment_job_standard_list">
        <action
            android:id="@+id/action_jobStandardListFragment_to_jobStandardFragment"
            app:destination="@id/jobStandardFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
    </fragment>
    <fragment
        android:id="@+id/boardSettingFragment"
        android:name="com.ztkj.vibepress.ui.fragment.setting.BoardSettingFragment"
        android:label="BoardSettingFragment"
        tools:layout="@layout/fragment_board_setting" />
    <fragment
        android:id="@+id/calibrationFragment"
        android:name="com.ztkj.vibepress.ui.fragment.setting.CalibrationFragment"
        android:label="CalibrationFragment"
        tools:layout="@layout/fragment_calibration" />
    <action
        android:id="@+id/action_global_jobStandardListFragment"
        app:destination="@id/jobStandardListFragment"
        app:enterAnim="@anim/slide_in_right"
        app:exitAnim="@anim/slide_out_left"
        app:popEnterAnim="@anim/slide_in_left"
        app:popExitAnim="@anim/slide_out_right" />
    <fragment
        android:id="@+id/advancedSettingFragment"
        tools:layout="@layout/fragment_advanced_setting"
        android:name="com.ztkj.vibepress.ui.fragment.setting.AdvancedSettingFragment"
        android:label="AdvancedSettingFragment">
        <action
            android:id="@+id/action_advancedSettingFragment_to_netParamFragment"
            app:destination="@id/netParamFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
        <action
            android:id="@+id/action_advancedSettingFragment_to_serialParamFragment"
            app:destination="@id/serialParamFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
        <action
            android:id="@+id/action_advancedSettingFragment_to_deviceAttributeFragment"
            app:destination="@id/deviceAttributeFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
        <action
            android:id="@+id/action_advancedSettingFragment_to_boardSettingFragment"
            app:destination="@id/boardSettingFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
        <action
            android:id="@+id/action_advancedSettingFragment_to_multiSettingFragment"
            app:destination="@id/multiSettingFragment" />
    </fragment>
    <fragment
        android:id="@+id/multiSettingFragment"
        android:name="com.ztkj.vibepress.ui.fragment.setting.MultiSettingFragment"
        android:label="MultiSettingFragment"
        tools:layout="@layout/fragment_multi_setting" />

</navigation>