<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">


    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" /> <!-- 存储权限 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission
        android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" /> <!-- 定位 -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.VIBRATE" />

    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission
        android:name="android.permission.WRITE_SETTINGS"
        tools:ignore="ProtectedPermissions" /> <!-- 相机 -->
    <uses-permission android:name="android.permission.CAMERA" />
    <!-- Android O INSTALL PERMISSION（安装app权限） -->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <!-- Android R 获取应用列表 -->
    <uses-permission
        android:name="android.permission.QUERY_ALL_PACKAGES"
        tools:ignore="QueryAllPackagesPermission" /> <!-- 指定的OpenGL ES版本 -->
    <!-- 静默安装 -->
    <uses-permission
        android:name="android.permission.INSTALL_PACKAGES"
        tools:ignore="ProtectedPermissions" />

    <uses-feature
        android:glEsVersion="0x00020000"
        android:required="true" /> <!-- 开机启动广播 -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-feature
        android:glEsVersion="0x00020000"
        android:required="true" /> <!-- 开机启动广播 -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher"
        android:supportsRtl="true"
        android:name=".App"
        android:largeHeap="true"
        android:usesCleartextTraffic="true"
        android:theme="@style/Theme.VibePress"
        tools:targetApi="31">
        <activity
            android:name=".ui.activity.SplashActivity"
            android:screenOrientation="landscape"
            android:theme="@style/FullscreenTheme"
            android:exported="true">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".ui.activity.ErrorActivity"
            android:exported="false" />

        <activity
            android:name=".ui.activity.MainActivity"
            android:windowSoftInputMode="adjustResize"
            android:exported="true">


            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>

        <service android:name=".service.BackgroundService" />

        <!--  Auto Start -->
        <receiver
            android:name=".service.AutoStartBroadReceiver"
            android:exported="true">
            <intent-filter android:priority="9999">
                <action android:name="android.intent.action.BOOT_COMPLETED" />

                <category android:name="android.intent.category.HOME" />
            </intent-filter>
        </receiver>
    </application>

</manifest>