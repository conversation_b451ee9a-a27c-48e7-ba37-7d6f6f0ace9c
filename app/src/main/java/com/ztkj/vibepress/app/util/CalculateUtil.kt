package com.ztkj.vibepress.app.util

import android.util.Log
import androidx.lifecycle.MutableLiveData
import com.ztkj.baselib.ext.util.notNull
import com.ztkj.vibepress.appViewModel
import com.ztkj.vibepress.data.Constants
import com.ztkj.vibepress.data.Constants.TAMP.Companion.HAMMER_STATE_DOWN
import com.ztkj.vibepress.data.Constants.TAMP.Companion.HAMMER_STATE_PEAK
import com.ztkj.vibepress.data.Constants.TAMP.Companion.HAMMER_STATE_TROUGH
import com.ztkj.vibepress.data.Constants.TAMP.Companion.HAMMER_STATE_UP
import com.ztkj.vibepress.data.Constants.TAMP.Companion.MIN_TENSION_VALUE
import com.ztkj.vibepress.data.Constants.TAMP.Companion.TENSION_CHANGE_LIFT
import com.ztkj.vibepress.data.Constants.TAMP.Companion.TENSION_CHANGE_NO
import com.ztkj.vibepress.data.Constants.TAMP.Companion.TENSION_CHANGE_RELEASE
import com.ztkj.vibepress.data.Constants.TAMP.Companion.TENSION_STATE_HAS
import com.ztkj.vibepress.data.Constants.TAMP.Companion.TENSION_STATE_NO
import com.ztkj.vibepress.data.model.bean.HammerDataDTO
import com.ztkj.vibepress.data.model.bean.TampDeviceDTO
import java.math.BigDecimal
import java.math.RoundingMode
import java.util.Timer
import java.util.TimerTask
import java.util.regex.Pattern


/**
 * @CreateTime : 2023/5/11 14:52
 * <AUTHOR> AppOS
 * @Description :
 */
object CalculateUtil {

    private const val TAG = "CalculateUtil"

    //上一条夯锤数据
    private var lastHammerDataDTO: HammerDataDTO? = null

    val lastHammer: MutableLiveData<HammerDataDTO> = MutableLiveData()

    //当前夯锤数据
    private var currentHammerDataDTO: HammerDataDTO? = null


    //上上一条锤高
    private var beforeLastHeight = 0.0

    /**
     * 拉锁实体类
     */
    private val tampDeviceDTO: TampDeviceDTO = TampDeviceDTO(1)
//    private val tampDeviceDTO: TampDeviceDTO = TampDeviceDTO(4)

    private val timer = Timer()

    private val timerTask = object : TimerTask() {
        override fun run() {
            appViewModel.hammerDataDTO.postValue(currentHammerDataDTO)
        }

    }

    var tensionValue: Double = 0.0

    //匹配传感器数据
    private val TENSION_DATA: Pattern =
        Pattern.compile("^\\\$D,(\\d+),([01]),([0-9.]+)\\*(\\w{2})\\r\\n\$")

    init {
//        timer.schedule(timerTask,0,1000L)
    }

    fun release() {
        timer.cancel()
        timerTask.cancel()
    }


    /**
     * 处理传感器数据
     * @param sensorDataStr 传感器数据
     */
    fun processSensorData(sensorDataStr: String) {
        //解析传感器数据
        parseSensorData(sensorDataStr)
        //波峰、波谷、张力变化
        lastHammerDataDTO?.run {
            if (this.hammerState > HAMMER_STATE_DOWN || this.tensionChange > TENSION_CHANGE_NO) {
                uploadOriginalData()
                appViewModel.hammerDataDTO.postValue(lastHammerDataDTO)
            }
        }
    }

    /**
     * @return Pair<String?, Int?>? 传感器计数器值和张力状态
     */
    fun parseSensorCounter(sensorDataStr: String): Pair<String?, Int?>? {
        //解析传感器数据获取计数器值和张力值
        val split = parseVibeContent(data = sensorDataStr)
        if (split != null && split.size > 3) {
            val tensionValue = split[3].split("*")[0].toFloat()
            val tensionState = hasTension(tensionValue)
            return Pair(split[1], tensionState)
        }
        return null
    }

    fun parseHammerHeight(sensorDataStr: String): Double {
        val sensorValue = parseSensorCounter(sensorDataStr)?.first?.toInt()
        if (sensorValue != null) {
            return calculateHammerHeight(sensorValue)
        }
        return 0.0
    }


    //$D,123,1,0.5*FF\r\n
    private fun parseSensorData(sensorDataStr: String) {
        //解析传感器数据获取计数器值和张力值
        val split = parseVibeContent(data = sensorDataStr)
        //如果读取到的数据异常,不处理
        split?.run {
            //计数器值
            val sensorValue = split[1].toInt()
            //张力状态（0:无张力；1:有张力）
            val tensionDefault = split[2].toInt()
            //TODO 这里应该要修改一下
            val tensionValue = split[3].split("*")[0].toFloat()
            val tensionState = hasTension(tensionValue)
            val currentHeight = calculateHammerHeight(sensorValue)
            appViewModel.hammerHeight.postValue(currentHeight.toFloat())
            /*            Log.e(
                            TAG,
                            "解析传感器数据:$sensorDataStr --> $sensorValue,$tensionValue,$tensionState, 锤高:${currentHeight},${CacheUtil.getHammerHeightRatio()}",
                        )*/
            lastHammerDataDTO.notNull({
                lastHammerDataDTO = currentHammerDataDTO?.copy()
                setHammerDataDTO(sensorValue, currentHeight, tensionState)
            }, {
                initHammerDataDTO(sensorValue, currentHeight, tensionState)
            })
            appViewModel.hammerDataDTO.postValue(lastHammerDataDTO)
        }

    }

    private fun calculateHammerHeight(sensorValue: Int): Double {
        //传感器的值转换为锤高系数
        val tensionValueWithHammerRatio = sensorValue.toDouble() / CacheUtil.getHammerHeightRatio()
        return BigDecimal.valueOf(tensionValueWithHammerRatio)
            .setScale(2, RoundingMode.HALF_UP)
            .toDouble()
        /*        val tensionValueWithHammerRatio = sensorValue / CacheUtil.getHammerHeightRatio()
                return BigDecimal.valueOf(tensionValueWithHammerRatio.toLong())
                    .divide(
                        BigDecimal.valueOf(tampDeviceDTO.cableCount.toLong()),
                        3,
                        RoundingMode.HALF_UP
                    )
                    .toDouble()*/
    }

    /**
     * 判断是否有张力值
     */
    private fun hasTension(tensionValue: Float) = when {
        tensionValue <= MIN_TENSION_VALUE -> 0
        tensionValue < CacheUtil.getTensionValue() -> 1
        else -> 2
    }


    /**
     * 判断是否是合法的内容
     * $D,251,0,0.00*70
     * ^\$D,(\d+),([01]),([0-9.]+)\*(\w{2})\r\n$
     */
    private fun parseVibeContent(data: String): List<String>? {
        if (!data.startsWith("\$D"))
            return null
        val split = data.split(",")
        return if (split.size >= 4 && split[3].split("*").isNotEmpty()) split else null
    }

    private fun setHammerDataDTO(sensorValue: Int, currentHeight: Double, tensionState: Int) {
        currentHammerDataDTO?.apply {
            this.sensorValue = sensorValue
            this.hammerHeight = currentHeight
            this.tensionState = tensionState
            //张力变化事件（0：无变化；1：提锤；2：放锤）
            this.tensionChange = calculateTensionChange(tensionState)
            //上一次锤状态(1:上升；2：波峰；3：下降；4：波谷)
            this.hammerState = calculateHammerState(currentHeight)
        }
    }

    /**
     * 张力变化事件（0：无变化；1：提锤；2：放锤）
     * @param tensionState
     * @return
     */
    private fun calculateTensionChange(tensionState: Int): Int {
        if (lastHammerDataDTO!!.tensionState == TENSION_STATE_NO && tensionState == TENSION_STATE_HAS) {
            return TENSION_CHANGE_LIFT
        } else if (lastHammerDataDTO!!.tensionState == TENSION_STATE_HAS && tensionState == TENSION_STATE_NO) {
            return TENSION_CHANGE_RELEASE
        }
        return TENSION_CHANGE_NO
    }

    /**
     * 计算上一次夯锤状态
     * @param currentHeight 当前夯锤高度
     * @return 夯锤状态，见 TampConstant 类定义的常量
     */
    private fun calculateHammerState(currentHeight: Double): Int {
        var state: Int = HAMMER_STATE_DOWN
        val lastHeight = lastHammerDataDTO!!.hammerHeight
        if (currentHeight < lastHeight && lastHeight >= beforeLastHeight) {
            // 出现波峰
            state = HAMMER_STATE_PEAK
        } else if (currentHeight > lastHeight && lastHeight <= beforeLastHeight) {
            // 出现波谷
            state = HAMMER_STATE_TROUGH
        } else if (currentHeight >= lastHeight && lastHeight > beforeLastHeight) {
            // 上升
            state = HAMMER_STATE_UP
        }
        // 更新上上次锤高
        setBeforeLastHeight(lastHeight)
        return state
    }

    /**
     * 初始化锤数据
     * @param sensorValue
     * @param currentHeight
     * @param tensionState
     */
    private fun initHammerDataDTO(sensorValue: Int, currentHeight: Double, tensionState: Int) {
        currentHammerDataDTO = HammerDataDTO(
            sensorValue,
            currentHeight,
            tensionState,
            0f,
            Constants.TAMP.TENSION_CHANGE_NO,
            Constants.TAMP.HAMMER_STATE_DOWN
        )
        lastHammerDataDTO = currentHammerDataDTO?.copy()
        setBeforeLastHeight(currentHeight)
    }

    /**
     * 更新上上次锤高
     */
    private fun setBeforeLastHeight(currentHeight: Double) {
        beforeLastHeight = currentHeight
    }

    private fun saveToLocalDatabase() {

    }


    private fun uploadOriginalData() {
        println("上报：" + lastHammerDataDTO.toString())
        //上报 lastHammerDataDTO +  GPS  至服务端
    }
}