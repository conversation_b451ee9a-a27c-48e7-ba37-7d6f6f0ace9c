package com.ztkj.vibepress.app.util;

public class FilterUtil {
    static int FILTER_DEEP = 5; // 滤波深度
    double buf[] = new double[FILTER_DEEP];
    double percent;
    int i=0;
    public FilterUtil(double percent) {
        this.percent = percent;
    }

    public double getResult(double dt){

        double sum = 0;
        int cnt = 0;

        for(int j = 0; j<FILTER_DEEP;j++) {
            if(buf[j] != 0) {
                sum += buf[j];
                cnt++;
            }
        }

        if(cnt>0) {
            sum = sum/cnt;
        }

        if(sum!=0) {
            if(Math.abs(sum-dt)/sum <= percent)
                sum = dt;
            else {
                sum = (sum*cnt + dt)/(cnt+1);
            }
        } else
            sum = dt;

        this.buf[i] = sum;
        i = i>= FILTER_DEEP-1 ? 0 : i+1;

        return sum;
    }

    public static void main(String[] args){
        System.out.println("test main");
        FilterUtil fil = new FilterUtil((double) 0.5);

        double[] test = new double[]{(double) 25.1,(double) 26.4,(double) 27.5,(double) 49.1,(double) 28.4,(double) 35.1,(double) 48.1};

        int i = 0;
        while(true)
        {
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                // TODO 自动生成的 catch 块
                e.printStackTrace();
            }


            double t = fil.getResult(test[i]);
            System.out.println("输入值："+test[i]+"\t输出值："+t);
            if(i++>=7-1)
                i=0;
        }

    }
}
