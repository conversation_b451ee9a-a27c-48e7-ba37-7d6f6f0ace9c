package com.ztkj.vibepress.app.db

import androidx.room.TypeConverter
import java.math.BigDecimal

/**
 * @CreateTime : 2023/6/27 10:47
 * <AUTHOR> AppOS
 * @Description :
 */
class Converter {
    companion object {
        @TypeConverter
        @JvmStatic
        fun fromBigDecimal(value: BigDecimal): String {
            return value.toString()
        }

        @TypeConverter
        @JvmStatic
        fun toBigDecimal(value: String): BigDecimal {
            return value.toBigDecimal()
        }
    }
}