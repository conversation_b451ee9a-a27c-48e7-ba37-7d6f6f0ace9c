package com.ztkj.vibepress.app.ext

import com.ztkj.sensorlib.helper.SerialPortHelper

/**
 * @CreateTime : 2023/6/1 10:35
 * <AUTHOR> AppOS
 * @Description :
 */

/**
 * 初始化RTK命令
 */
fun SerialPortHelper.initRTKCommand() {
    if (isOpen) {
        this.sendTxt("gpgga 1\r\n")
        this.sendTxt("gpvtg 1\r\n")
//        this.sendTxt("gptra 1\r\n")
        this.sendTxt("gpzda 1\r\n")
//        this.sendTxt("gprmc 1\r\n")
        this.sendTxt("gphdt  1\r\n")
        this.sendTxt("saveconfig\r\n")
    }
}
