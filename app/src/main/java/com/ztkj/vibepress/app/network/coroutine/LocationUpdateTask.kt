package com.ztkj.vibepress.app.network.coroutine

import kotlinx.coroutines.*

/**
 * Update Location Async Task
 */
class LocationUpdateTask : UpdateTask{
    private var scope: CoroutineScope? = null

    override fun scheduleUpdate(interval: Long) {
        cancel()
        val scope = CoroutineScope(Dispatchers.IO)
        scope.launch {
            while (isActive) {
                try {
//                    appViewModel.lastGeoPoint.value?.run {
//                        val updateLocationRequest = UpdateLocationRequest(this.lon,this.lat)
//                        apiService.uploadLocation(initHttpJsonBody(updateLocationRequest))
//                    }
                }catch (e: Exception) {
                    e.printStackTrace()
                }
                delay(interval)
            }
        }
        this.scope = scope
    }

    override fun cancel() {
        scope?.cancel()
        scope = null
    }


}