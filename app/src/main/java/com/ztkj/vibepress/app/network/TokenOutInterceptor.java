package com.ztkj.vibepress.app.network;


import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.StringUtils;
import com.ztkj.vibepress.AppKt;
import com.ztkj.vibepress.app.util.CacheUtil;
import com.ztkj.vibepress.data.model.bean.LoginClient;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

import okhttp3.Interceptor;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okio.Buffer;
import okio.BufferedSource;
import retrofit2.Call;

/**
 * @CreateTime : 2022/8/9 14:08
 * <AUTHOR> AppOS
 * @Description :
 */
public class TokenOutInterceptor implements Interceptor {
    private final String invalid_token = "请求未授权";
    /**
     * accessToken
     */
    private final static String ACCESS_TOKEN_NAME = "Blade-Auth";

    private final static String AUTHORIZATION = "Authorization";

    private final static String AUTHORIZATION_VALUE = "Basic YW5kcm9pZDphbmRyb2lkX3NlY3JldA==";

    private static final Charset UTF8 = StandardCharsets.UTF_8;

    @NonNull
    @Override
    public Response intercept(Chain chain) throws IOException {
        // 拿到我们的请求
        Request original = chain.request();
        // 重新进行build
        Request.Builder builder = original.newBuilder();
        String accessToken = CacheUtil.getAccessToken();
        builder.addHeader(AUTHORIZATION, AUTHORIZATION_VALUE);
        //全局添加请求头token
        if (!TextUtils.isEmpty(accessToken)) {
            // 注入一个token
            builder.addHeader(ACCESS_TOKEN_NAME, "bearer  " + accessToken);
        }
        //获取response
        Response response = chain.proceed(builder.build());
        //判断token是否过期,根据refresh_token刷新accessToken
        if (isTokenExpired(response)) {

            String refreshToken = CacheUtil.getRefreshToken();
//            assert refreshToken != null;
            Call<LoginClient> call = AppRetrofitKt.getApiService().refreshToken(refreshToken, "refresh_token", "all", "000000");
            LoginClient loginRspModel = call.execute().body();
            if (loginRspModel != null && !StringUtils.isEmpty(loginRspModel.getAccessToken())) {
                CacheUtil.setUser(loginRspModel);
                //使用新的Token，创建新的请求
                Request newRequest = chain.request()
                        .newBuilder()
                        .addHeader(AUTHORIZATION, AUTHORIZATION_VALUE)
                        .addHeader(ACCESS_TOKEN_NAME, "bearer  " + loginRspModel.getAccessToken())
                        .build();
                //重新请求
                response.close();
                return chain.proceed(newRequest);
            } else {
                //Refresh Token过期处理
                AppKt.getEventViewModel().getOutRefreshToken().postValue(true);
            }

        }
        return response;

    }


    /**
     * 根据Response，判断Token是否失效
     * 若code == 10086 则失效
     *
     * @param response
     * @return
     */
    private boolean isTokenExpired(Response response) {
        try {
            ResponseBody responseBody = response.body();
            BufferedSource source = responseBody.source();
            source.request(Long.MAX_VALUE); // Buffer the entire body.
            Buffer buffer = source.getBuffer();
            Charset charset = UTF8;
            MediaType contentType = responseBody.contentType();
            if (contentType != null) {
                charset = contentType.charset(UTF8);
            }
            //获取响应体的字符串
            String bodyString = buffer.clone().readString(charset);
            JSONObject jsonObject = new JSONObject(bodyString);
            int code = jsonObject.optInt("code");
            String msg = jsonObject.optString("msg");
            if (code == 401 || invalid_token.equals(msg)) {
                return true;
            }
        } catch (IOException | JSONException e) {
            e.printStackTrace();
        }
        return false;
    }
}
