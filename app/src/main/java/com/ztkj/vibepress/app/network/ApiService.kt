package com.ztkj.vibepress.app.network

import com.ztkj.vibepress.data.model.bean.ApiResponse
import com.ztkj.vibepress.data.model.bean.JobStandardEntity
import com.ztkj.vibepress.data.model.bean.LoginClient
import com.ztkj.vibepress.data.model.bean.ProcessEntity
import com.ztkj.vibepress.data.model.bean.TampDeviceEntity
import com.ztkj.vibepress.data.model.bean.TampGpsDetailEntity
import retrofit2.Call
import retrofit2.http.*

/**
 * @CreateTime : 2022/10/17 9:42
 * <AUTHOR> AppOS
 * @Description :
 */
interface ApiService {

    companion object {
        /**
         * 接口改成带API的 夯机和机场同一个平台
         */

        const val BASE_URL = "http://***********:9713/api/"
    }

    /**
     * 登录
     * @param username 用户名
     * @param password encode with MD5
     * @param grantType default: password
     * @param scope default: all
     * @param tenantId default: 000000
     * @return LoginClient
     */
    @FormUrlEncoded
    @Headers("Tenant-Id:000000")
    @POST("blade-auth/oauth/token")
    @Token(value = false)
    suspend fun login(
        @Field("username") username: String,
        @Field("password") password: String,
        @Field("grant_type") grantType: String = "password",
        @Field("scope") scope: String = "all",
        @Field("tenantId") tenantId: String = "000000"
    ): LoginClient

    /**
     * refreshToken
     */
    @FormUrlEncoded
    @Headers("Tenant-Id:000000")
    @POST("blade-auth/oauth/token")
    @Token(value = false)
    fun refreshToken(
        @Field("refresh_token") refreshToken: String,
        @Field("grant_type") grantType: String = "refresh_token",
        @Field("scope") scope: String = "all",
        @Field("tenantId") tenantId: String = "000000"
    ): Call<LoginClient>

    /**
     *
     */
    @POST("szh-tampBase/jobStandard/save")
    suspend fun updateJobStandard(@Body jobStandardEntity: JobStandardEntity): ApiResponse<String>

    /**
     * 获取设备属性
     * @param serialNum 序列号
     */
    @GET("szh-tampBase/tampDevice/detailPad")
    suspend fun getDeviceAttribute(@Query("serialNum") serialNum: String): ApiResponse<TampDeviceEntity>

    /**
     * 更新设备属性
     */
    @POST("szh-tampBase/tampDevice/update")
    suspend fun updateTampDeviceEntity(@Body tampDeviceEntity: TampDeviceEntity): ApiResponse<Nothing>

    @GET("szh-tampBase/jobStandard/listBySerialNum")
    suspend fun getJobStandardList(@Query("serialNum") serialNum: String): ApiResponse<List<JobStandardEntity>>

    /**
     * Delete JobStandard
     */
    @FormUrlEncoded
    @POST("szh-tampBase/jobStandard/remove")
    suspend fun deleteJobStandard(@Field("ids") ids: String): ApiResponse<Nothing>

    @GET("szh-tampGpsdata/tampGpsData/hjProcessProperty")
    suspend fun getHjProcessProperty(@Query("deviceIds") deviceIds: String?): ApiResponse<List<ProcessEntity>>

    /**
     * 根据设备ID获取GPS数据详情
     * @param deviceId 设备ID
     */
    @GET("szh-tampGpsdata/tampGpsData/detailById")
    suspend fun getTampGpsDataById(@Query("deviceId") deviceId: String): ApiResponse<TampGpsDetailEntity>

}