package com.ztkj.vibepress.app.ext

import android.annotation.SuppressLint
import androidx.fragment.app.Fragment
import com.afollestad.materialdialogs.DialogBehavior
import com.afollestad.materialdialogs.DialogCallback
import com.afollestad.materialdialogs.LayoutMode
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.ModalDialog
import com.afollestad.materialdialogs.bottomsheets.BottomSheet
import com.afollestad.materialdialogs.customview.customView
import com.afollestad.materialdialogs.lifecycle.lifecycleOwner
import com.afollestad.materialdialogs.list.listItemsSingleChoice
import com.ztkj.sensorlib.utils.SerialPortUtils
import com.ztkj.vibepress.R
import java.lang.ref.WeakReference


typealias SingleChoiceStringListener =
        ((index: Int, text: CharSequence, textValue: String) -> Unit)?

typealias SingleChoiceIntListener =
        ((index: Int, text: CharSequence, textValue: Int) -> Unit)?

@SuppressLint("CheckResult")
fun showAvailableBaudrate(
    fragment: WeakReference<Fragment>,
    initialSelection: Int = -1,
    section: SingleChoiceStringListener = null
) {
    fragment.get()?.run {
        MaterialDialog(this.requireContext()).show {
            title(text = "波特率")
            lifecycleOwner(viewLifecycleOwner)
            listItemsSingleChoice(
                items = SerialPortUtils.getAllBaudrate().toList(),
                initialSelection = initialSelection
            ) { _, index, text ->
                if (section != null) {
                    section(index, text, text.toString())
                }
            }
        }
    }
}


@SuppressLint("CheckResult")
fun showSerialPortDevicePathDialog(
    fragment: WeakReference<Fragment>,
    initialSelection: Int = -1,
    section: SingleChoiceStringListener = null
) {
    fragment.get()?.run {
        MaterialDialog(this.requireContext()).show {
            title(text = "选择串口")
            lifecycleOwner(viewLifecycleOwner)
            listItemsSingleChoice(
                items = SerialPortUtils.getAllDevicesPath().toList(),
                initialSelection = initialSelection
            ) { _, index, text ->
                if (section != null) {
                    section(index, text, text.toString())
                }
            }
        }
    }
}

/**
 * 设备安装方式Dialog
 */
@SuppressLint("CheckResult")
fun showDeviceInstallationDialog(
    fragment: WeakReference<Fragment>,
    initialSelection: Int? = 0,
    section: SingleChoiceStringListener = null
) {
    fragment.get()?.run {
        MaterialDialog(this.requireContext(), BottomSheet(LayoutMode.WRAP_CONTENT)).show {
            title(text = "设备安装方式")

            listItemsSingleChoice(
                res = R.array.install_way,
                initialSelection = initialSelection ?: 0
            ) { _, index, text ->
                if (section != null) {
                    section(index, text, text.toString())
                }
                "Selected item $text at index $index".toast()
            }
            lifecycleOwner(viewLifecycleOwner)
        }
    }
}

@SuppressLint("CheckResult")
fun showCreateJobStandardDialog(
    fragment: WeakReference<Fragment>,
    click: DialogCallback? = null
) {
    fragment.get()?.run {
        MaterialDialog(this.requireContext()).show {
            cancelOnTouchOutside(false)
            message(R.string.empty_job_standard_tips)
            positiveButton(R.string.create, click = click)
            negativeButton(R.string.cancel)
            lifecycleOwner(viewLifecycleOwner)
        }
    }
}

@SuppressLint("CheckResult")
fun showChangeRammerDialog(
    fragment: WeakReference<Fragment>,
    dialogBehavior: DialogBehavior = ModalDialog,
    click: DialogCallback? = null
) {
    fragment.get()?.run {
        MaterialDialog(requireContext(), dialogBehavior).show {
            title(R.string.tip)
            icon(R.mipmap.ic_tips)
            customView(R.layout.layout_change_rammer, scrollable = true, horizontalPadding = true)
            positiveButton(R.string.confirm, click = click)
            negativeButton(android.R.string.cancel) {
                dismiss()
            }
            lifecycleOwner(viewLifecycleOwner)
            cancelOnTouchOutside(false)
            noAutoDismiss()
        }
    }
}

/**
 * Show Basic + Content Material Dialog
 */
@SuppressLint("CheckResult")
fun showNormalMaterialDialog(
    fragment: WeakReference<Fragment>,
    content: String,
    click: DialogCallback? = null
) {
    fragment.get()?.run {
        MaterialDialog(requireContext()).show {
            title(R.string.tip)
            icon(R.mipmap.ic_tips)
            message(text = content)
            positiveButton(R.string.confirm, click = click)
            negativeButton(android.R.string.cancel) {
                dismiss()
            }
            lifecycleOwner(viewLifecycleOwner)
            cancelOnTouchOutside(false)
            noAutoDismiss()
        }
    }
}

private fun showAlertDialog() {

}

