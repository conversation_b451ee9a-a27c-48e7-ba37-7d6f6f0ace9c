package com.ztkj.vibepress.app.network

import okhttp3.Interceptor
import okhttp3.Response
import org.json.JSONException
import org.json.JSONObject
import java.io.IOException
import java.nio.charset.StandardCharsets

/**
 * @CreateTime : 2022/12/1 14:16
 * <AUTHOR> AppOS
 * @Description :
 */
class TokenInterceptor : Interceptor {
    companion object {
        private const val invalid_token = "请求未授权"

        /**
         * accessToken
         */
        private const val ACCESS_TOKEN_NAME = "Blade-Auth"

        private const val AUTHORIZATION = "Authorization"

        private const val AUTHORIZATION_VALUE = "Basic YW5kcm9pZDphbmRyb2lkX3NlY3JldA=="

        private val UTF8 = StandardCharsets.UTF_8
    }

    override fun intercept(chain: Interceptor.Chain): Response {
        /*//拿到原始请求
        val original = chain.request()
        //重新 build
        val builder = original.newBuilder()
        //添加公共头
        builder.addHeader(AUTHORIZATION, AUTHORIZATION_VALUE)
        //拿到存储的AccessToken
        CacheUtil.getAccessToken()?.let {
            builder.addHeader(ACCESS_TOKEN_NAME,"bearer  $it")
        }
        //获取Response
        val response = chain.proceed(builder.build())
        //判断token是否过期,根据refresh_token刷新accessToken
        if (isTokenExpired(response)) {
            val refreshToken = CacheUtil.getRefreshToken()
            refreshToken?.run {
                val call = apiService.refreshToken(this)
                val loginResModel = call.execute().body()
                loginResModel?.run {
                    if (StringUtils.isNotEmpty(accessToken)) {
                        CacheUtil.setUser(this)
                        //使用新的Token，创建新的请求
                        val newRequest = chain.request()
                            .newBuilder()
                            .addHeader(AUTHORIZATION, AUTHORIZATION_VALUE)
                            .addHeader(ACCESS_TOKEN_NAME, "bearer  $this")
                            .build()
                        //重新请求
                        response.close()
                        return chain.proceed(newRequest)
                    }
                }
            }
        }*/
        return chain.proceed(chain.request())
    }

    private fun isTokenExpired(response: Response): Boolean {
        try {
            val responseBody = response.body()
            val source = responseBody?.source()
            source?.request(Long.MAX_VALUE)
            val contentType = responseBody?.contentType()
            val charset = contentType?.charset(UTF8)
            //获取响应的字符串
            charset?.let {
                val bodyString = source?.buffer?.readString(charset)
                bodyString?.run {
                    val jsonObject = JSONObject(this)
                    val code = jsonObject.optInt("code")
                    val msg = jsonObject.optString("msg")
                    if (code == 401 || invalid_token == msg) {
                        return true
                    }
                }
            }
        } catch (e: IOException) {
            e.printStackTrace()
        } catch (e: JSONException) {
            e.printStackTrace()
        }
        return false
    }
}