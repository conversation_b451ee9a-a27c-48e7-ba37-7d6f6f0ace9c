package com.ztkj.vibepress.app.util;

import com.google.gson.Gson;
import com.ztkj.imext.bean.SingleMessage;
import com.ztkj.imext.im.MessageType;
import com.ztkj.sensorlib.model.GpsdataInfo;
import com.ztkj.sensorlib.utils.DateUtil;
import com.ztkj.sensorlib.utils.NmeaAnalysisUtil;
import com.ztkj.vibepress.data.model.bean.TampGpsDataEntity;
import com.ztkj.vibepress.data.model.bean.VibeMetaData;

import java.util.UUID;

public class MessageUtil {

    private static MessageUtil messageUtil = null;
    private FilterUtil fil = new FilterUtil((double) 0.5);

    public static MessageUtil getInstance() {
        synchronized (MessageUtil.class) {
            if (messageUtil == null)
                messageUtil = new MessageUtil();
        }
        return messageUtil;
    }

    /**
     * 位移传感器数据
     *
     * @param deviceSerialNumber
     * @param depth
     * @return
     */
    public SingleMessage getDisplacementMsg(String deviceSerialNumber, float depth) {
        SingleMessage message = new SingleMessage();
        message.setMsgId(UUID.randomUUID().toString());
        message.setMsgType(MessageType.SINGLE_CHAT.getMsgType());
        message.setMsgContentType(MessageType.MessageContentType.TEXT.getMsgContentType());
        message.setFromId(deviceSerialNumber);
        message.setToId("100001");
        long timestamp = System.currentTimeMillis();
        message.setTimestamp(System.currentTimeMillis());
        message.setContent(depth + "");
        return message;
    }

    /**
     * 将本地存储的VibeMetaData对象，转换为需要发送给服务器的SingleMessage对象
     *
     * @param deviceSerialNumber
     * @param vibeMetaData
     * @return
     */
    public SingleMessage getMsgFromLocal(String deviceSerialNumber, VibeMetaData vibeMetaData) {
        SingleMessage message = new SingleMessage();
        message.setMsgId(UUID.randomUUID().toString());
        message.setMsgType(MessageType.SINGLE_CHAT.getMsgType());
        message.setFromId(deviceSerialNumber);
        message.setToId("100001");
        message.setMsgContentType(MessageType.MessageContentType.TEXT.getMsgContentType());
        message.setTimestamp(vibeMetaData.getTimestamp());
        message.setContent(vibeMetaData.getBody());
        return message;
    }

    public SingleMessage getMsg(String deviceSerialNumber, NmeaAnalysisUtil nmeaAnalysisUtil) {
        SingleMessage message = new SingleMessage();
        message.setMsgId(UUID.randomUUID().toString());
        message.setMsgType(MessageType.SINGLE_CHAT.getMsgType());
        message.setMsgContentType(MessageType.MessageContentType.TEXT.getMsgContentType());
        message.setFromId(deviceSerialNumber);
        message.setToId("100001");
        long timestamp = System.currentTimeMillis();
        message.setTimestamp(System.currentTimeMillis());
//        message.setContent(getSendContent(deviceSerialNumber, timestamp, nmeaAnalysisUtil));
        message.setContent(getNewSendContent(deviceSerialNumber, nmeaAnalysisUtil));
        return message;
    }

    /**
     * 新的压实发送实体
     *
     * @return
     */
    public String getNewSendContent(String deviceSerialNumber, NmeaAnalysisUtil nmeaAnalysisUtil) {
        GpsdataInfo gpsdataInfo = getGpsdataInfo(nmeaAnalysisUtil);
        final TampGpsDataEntity tampGpsDataEntity = NmeaDataToTampGpsDataEntity.transform(deviceSerialNumber, gpsdataInfo, HammerDTOUtils.getHammerValue());
        return new Gson().toJson(tampGpsDataEntity);
    }

    public String getSendContent(String deviceSerialNumber, long frameNumber, NmeaAnalysisUtil nmeaAnalysisUtil) {
        GpsdataInfo gpsdataInfo = getGpsdataInfo(nmeaAnalysisUtil);
        String netCptDataStr = connectNetVibeDataToStr(gpsdataInfo);
        String content = null;
        if (null != netCptDataStr) {
            content = packNetCptDataStrToFullFrame(deviceSerialNumber, frameNumber, netCptDataStr);
        }
        System.out.println("发送数据的content: " + content);
        return content;
    }

    public GpsdataInfo getGpsdataInfo(NmeaAnalysisUtil nmeaAnalysisUtil) {
        GpsdataInfo gpsdataInfo = new GpsdataInfo();
        //修正时间为负数的格式
        if (nmeaAnalysisUtil.getLocalTime() != null && nmeaAnalysisUtil.getLocalTime().getTime() > 0) {
            gpsdataInfo.setGpsTime(String.valueOf(nmeaAnalysisUtil.getLocalTime().getTime()));
        } else {
            gpsdataInfo.setGpsTime(String.valueOf(DateUtil.getCurrentMillis()));
        }
        gpsdataInfo.setLongitude(DataUtil.DoubleToString(nmeaAnalysisUtil.getLongitude(), 8));
        gpsdataInfo.setLatitude(DataUtil.DoubleToString(nmeaAnalysisUtil.getLatitude(), 8));
        gpsdataInfo.setAltitude(DataUtil.DoubleToString(nmeaAnalysisUtil.getAltitude(), 3));
        gpsdataInfo.setSolution(String.valueOf(nmeaAnalysisUtil.getStatusType()));
        gpsdataInfo.setAmp(nmeaAnalysisUtil.getAmp());
        gpsdataInfo.setFreq(nmeaAnalysisUtil.getFreq());
        gpsdataInfo.setCmv(nmeaAnalysisUtil.getCmv());
        gpsdataInfo.setSatnum(String.valueOf(nmeaAnalysisUtil.getLockGnssCount()));//卫星数
        gpsdataInfo.setHdop(String.valueOf(nmeaAnalysisUtil.getHdop()));
        gpsdataInfo.setEllipsoid(DataUtil.DoubleToString(nmeaAnalysisUtil.getdUndulation(), 3));//椭球面高
        gpsdataInfo.setDiffdelay(String.valueOf(nmeaAnalysisUtil.getAge()));//差分延迟
        gpsdataInfo.setDiffstation(nmeaAnalysisUtil.getDiffstation());//差分站号
        gpsdataInfo.setDevicedir(DataUtil.DoubleToString(nmeaAnalysisUtil.getBearing(), 1)); //设备朝向
//        gpsdataInfo.setSpeed(DataUtil.DoubleToString(fil.getResult(nmeaAnalysisUtil.getfSpeed_KM_H()),2));
        gpsdataInfo.setSpeed(DataUtil.DoubleToString(nmeaAnalysisUtil.getfSpeed_KM_H(), 2));
        gpsdataInfo.setTempearature(nmeaAnalysisUtil.getTempearature());
//        gpsdataInfo.setAngle(nmeaAnalysisUtil);
        return gpsdataInfo;
    }

    /**
     * @param gpsdataInfo
     * @return 封装夯机数据
     */
    public String connectNetVibeDataToStr(GpsdataInfo gpsdataInfo) {
        final char segSeparator = ',';
        StringBuilder netCptDataSb = new StringBuilder();
        netCptDataSb.append(gpsdataInfo.getGpsTime())
                .append(segSeparator)
                .append(gpsdataInfo.getLatitude())
                .append(segSeparator)
                .append(gpsdataInfo.getLongitude())
                .append(segSeparator)
                .append(gpsdataInfo.getAltitude())
                .append(segSeparator)
                .append(gpsdataInfo.getSolution())
                .append(segSeparator)
                .append(gpsdataInfo.getSatnum())
                .append(segSeparator)
                .append(gpsdataInfo.getDiffdelay())
                .append(segSeparator)
                .append(gpsdataInfo.getDiffstation())
                .append(segSeparator)
                .append(gpsdataInfo.getEllipsoid())
                .append(segSeparator)
                .append(gpsdataInfo.getDevicedir())
                .append(segSeparator)
                .append(gpsdataInfo.getSpeed())
                .append(segSeparator)
                .append(gpsdataInfo.getLatitude())
                .append(segSeparator)
                .append(gpsdataInfo.getLongitude())
                .append(segSeparator)
                .append(HammerDTOUtils.parseHammerToNettyStr());
        return netCptDataSb.toString();
    }


    // 将净压实数据（未包括"CPT"前的、未包括"温度"后的）连接成字符串，并返回该字符串或null（有错误时）
    public String connectNetCptDataToStr(GpsdataInfo gpsdataInfo) {
        // 数据验证

        final String frameTypeName = "CPT";
        final char segSeparator = ',';
        StringBuilder netCptDataSb = new StringBuilder();
        netCptDataSb.append(frameTypeName).append(segSeparator)
                .append(gpsdataInfo.getGpsTime())
                .append(segSeparator)
                .append(gpsdataInfo.getLatitude())
                .append(segSeparator)
                .append(gpsdataInfo.getLongitude())
                .append(segSeparator)
                .append(gpsdataInfo.getAltitude())
                .append(segSeparator)
                .append(gpsdataInfo.getSolution())
                .append(segSeparator)
                .append(gpsdataInfo.getAmp())
                .append(segSeparator)
                .append(gpsdataInfo.getFreq())
                .append(segSeparator)
                .append(gpsdataInfo.getCmv())
                .append(segSeparator)
                .append(gpsdataInfo.getSatnum())
                .append(segSeparator)
                .append(gpsdataInfo.getHdop())
                .append(segSeparator)
                .append(gpsdataInfo.getEllipsoid())
                .append(segSeparator)
                .append(gpsdataInfo.getDiffdelay())
                .append(segSeparator)
                .append(gpsdataInfo.getDiffstation())
                .append(segSeparator)
                .append(gpsdataInfo.getDevicedir())
                .append(segSeparator)
                .append(gpsdataInfo.getSpeed())
                .append(segSeparator)
                .append(gpsdataInfo.getTempearature());
        return netCptDataSb.toString();
    }

    //将净压实数据字符串打包成一条完整的压实数据帧
    public static String packNetCptDataStrToFullFrame(String deviceSerialNumber, long frameNumber, String netCptStr) {
        final String dataCenterName = "H01";
        final char segSeparator = ',';
        final int realtimeDataStatusInteger = 1;        //实时数据状态标志数
        final int coWorkStatusInteger = 1;                //协同状态标志数
        StringBuilder realtimeSb = new StringBuilder();
        realtimeSb.append(dataCenterName).append(segSeparator)
                .append(deviceSerialNumber).append(segSeparator)
                //TODO 这里因为传入的System.currentTimeMillis()可能不准确，不要了
//                .append(frameNumber).append(segSeparator)
                .append(netCptStr).append(segSeparator)
                .append(realtimeDataStatusInteger)
                .append(segSeparator);
        //添加协同状态标志数
        realtimeSb.append(coWorkStatusInteger);
        //打包成NMEA格式
        String resultStr = NmeaAnalysisUtil.getInstance().packStringToNmeaFormat(realtimeSb.toString());
        return resultStr;
    }

    public String getDifMsg(String gpgga) {
        return gpgga;
    }

}
