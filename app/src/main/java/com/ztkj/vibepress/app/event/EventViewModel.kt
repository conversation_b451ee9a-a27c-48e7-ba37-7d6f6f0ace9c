package com.ztkj.vibepress.app.event

import com.kunminx.architecture.ui.callback.UnPeekLiveData
import com.ztkj.baselib.base.viewmodel.BaseViewModel
import com.ztkj.baselib.callback.livedata.event.EventLiveData
import com.ztkj.vibepress.data.model.kEnum.ServiceStatusType

/**
 * @CreateTime : 2022/10/13 17:29
 * <AUTHOR> AppOS
 * @Description : APP全局的ViewModel，可以在这里发送全局通知替代EventBus，LiveDataBus等
 */
class EventViewModel : BaseViewModel() {

    /*    */
    /**
     * POI搜索的结果
     *//*
    val poiSearchEvent: UnPeekLiveData<PoiResponse> =
        UnPeekLiveData.Builder<PoiResponse>().setAllowNullValue(true).create()

    */
    /**
     * 已选择的图层信息
     *//*
    var featureInfo = EventLiveData<Record>()

    */
    /**
     * 当前选中任务的详情
     *//*
    var selectedTaskDetail = EventLiveData<TaskDetailResponse>()

    */
    /**
     * 要素图层信息集合
     *//*
    var featureLayerInfoList = EventLiveData<List<Record>>()

    */
    /**
     * 首页控件是否显示
     *//*
    var mainViewVisible = BooleanLiveData()

    */
    /**
     * 本地选择的地图文件
     *//*
    var localMapFilePath: UnPeekLiveData<String> = UnPeekLiveData.Builder<String>().setAllowNullValue(true).create()

    */
    /**
     * 外部打开地图的文件
     *//*
    var externalImportFilePath = EventLiveData<String>()

    */

    /**
     * 当前的桩号
     */
    var currentStakeNumber = EventLiveData<String>()

    /**
     * 当前的击数
     */
    var currentHitCount = EventLiveData<String>()

    var outRefreshToken = EventLiveData<Boolean>()

    /**
     * 当前的传感器值
     */
    var currentSensorValue = EventLiveData<String>()


    /**
     * 后台服务的状态
     */
    var serviceStatusType: UnPeekLiveData<ServiceStatusType> =
        UnPeekLiveData.Builder<ServiceStatusType>().setAllowNullValue(false).create()

    init {
        serviceStatusType.value = ServiceStatusType.IDLE
    }

}