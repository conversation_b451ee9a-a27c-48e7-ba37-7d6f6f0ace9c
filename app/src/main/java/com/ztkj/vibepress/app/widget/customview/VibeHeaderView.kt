package com.ztkj.vibepress.app.widget.customview

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import com.ztkj.baselib.ext.util.setOnclickNoRepeat
import com.ztkj.vibepress.R
import com.ztkj.vibepress.app.ext.setCustomVisibility
import com.ztkj.vibepress.databinding.LayoutVibeHeaderViewBinding

/**
 * @CreateTime : 2023/6/8 11:25
 * <AUTHOR> AppOS
 * @Description :
 */
class VibeHeaderView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    var centerTitle: String? = null
        set(value) {
            field = value
            binding.centerTitle.text = value
        }

    private val binding: LayoutVibeHeaderViewBinding

    init {
        binding = LayoutVibeHeaderViewBinding.inflate(LayoutInflater.from(context), this, true)
        attrs?.let { applyAttributes(it) }
    }

    private fun applyAttributes(attrs: AttributeSet) {
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.VibeHeaderView)
        val centerTitle = typedArray.getString(R.styleable.VibeHeaderView_centerTitle)
        val showRightBtn = typedArray.getBoolean(R.styleable.VibeHeaderView_showRightBtn, false)
        val rightBtnTitle = typedArray.getString(R.styleable.VibeHeaderView_rightBtnTitle)
            ?: context.getString(R.string.save)
        val iconRes =
            typedArray.getResourceId(R.styleable.VibeHeaderView_rightBtnIcon, R.drawable.ic_save)
        binding.btnRight.setCustomVisibility(showRightBtn)
        if (showRightBtn) {
            binding.btnRight.setAttribute(rightBtnTitle, iconRes)
        }
        typedArray.recycle()

        binding.centerTitle.text = centerTitle
    }

    fun onBack(onClick: (View) -> Unit) {
        setOnclickNoRepeat(binding.back) {
            onClick.invoke(it)
        }
    }

    fun rightButtonClick(onClick: (View) -> Unit) {
        setOnclickNoRepeat(binding.btnRight, interval = 3000L) {
            onClick.invoke(it)
        }
    }

}