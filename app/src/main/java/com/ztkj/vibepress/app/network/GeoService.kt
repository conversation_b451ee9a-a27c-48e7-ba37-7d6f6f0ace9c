package com.ztkj.vibepress.app.network

import com.ztkj.app.mapcore.entity.GeoJsonModel
import okhttp3.OkHttpClient
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.http.GET
import retrofit2.http.QueryMap

/**
 * @CreateTime : 2023/8/10 15:18
 * <AUTHOR> AppOS
 * @Description :
 */
interface GeoService {
    companion object {
        const val BASE_URL = "http://*************:9709/"

        fun create(): GeoService {
            val client = OkHttpClient.Builder()
                .build()

            return Retrofit.Builder()
                .baseUrl(BASE_URL)
                .client(client)
                .addConverterFactory(GsonConverterFactory.create())
                .build()
                .create(GeoService::class.java)
        }
    }

    /**
     * 查询此wms层触摸点的geoJson数据
     *
     * @param map
     * @return
     */
    @GET("geoserver/tamp/wms")
    suspend fun geoWmsFeature(@QueryMap map: Map<String, String>): GeoJsonModel
}