package com.ztkj.vibepress.app.widget.customview

import android.app.Dialog
import android.content.Context
import androidx.fragment.app.Fragment
import com.ztkj.vibepress.R
import razerdp.basepopup.BasePopupWindow

/**
 * @CreateTime : 2023/1/17 10:27
 * <AUTHOR> AppOS
 * @Description :
 */
abstract class ZTBaseDialog: BasePopupWindow {

    abstract fun updateTextColor()

    constructor(fragment: Fragment) : super(fragment) {
    }

    constructor(context: Context) : super(context) {
    }

    constructor(dialog: Dialog) : super(dialog) {
    }

    override fun onShowing() {
        super.onShowing()
        updateTextColor()
    }

}