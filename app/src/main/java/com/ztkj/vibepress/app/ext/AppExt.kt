package com.ztkj.vibepress.app.ext

import android.os.Build
import android.text.TextUtils
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.navigation.NavController
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.WhichButton
import com.afollestad.materialdialogs.actions.getActionButton
import com.afollestad.materialdialogs.lifecycle.lifecycleOwner
import com.elvishew.xlog.LogConfiguration
import com.elvishew.xlog.LogLevel
import com.elvishew.xlog.XLog
import com.elvishew.xlog.flattener.ClassicFlattener
import com.elvishew.xlog.interceptor.BlacklistTagsFilterInterceptor
import com.elvishew.xlog.printer.AndroidPrinter
import com.elvishew.xlog.printer.file.FilePrinter
import com.elvishew.xlog.printer.file.naming.DateFileNameGenerator
import com.elvishew.xlog.printer.file.writer.SimpleWriter
import com.permissionx.guolindev.PermissionX
import com.permissionx.guolindev.callback.RequestCallback
import com.ztkj.vibepress.App
import com.ztkj.vibepress.BuildConfig
import com.ztkj.vibepress.app.util.CacheUtil
import com.ztkj.vibepress.app.util.SettingUtil
import java.io.BufferedReader
import java.io.File
import java.io.FileReader
import java.io.IOException
import kotlin.reflect.full.*

/**
 * @CreateTime : 2022/10/18 14:46
 * <AUTHOR> AppOS
 * @Description :
 */
/**
 * @param message 显示对话框的内容 必填项
 * @param title 显示对话框的标题 默认 温馨提示
 * @param positiveButtonText 确定按钮文字 默认确定
 * @param positiveAction 点击确定按钮触发的方法 默认空方法
 * @param negativeButtonText 取消按钮文字 默认空 不为空时显示该按钮
 * @param negativeAction 点击取消按钮触发的方法 默认空方法
 *
 */
fun AppCompatActivity.showMessage(
    message: String,
    title: String = "温馨提示",
    positiveButtonText: String = "确定",
    positiveAction: () -> Unit = {},
    negativeButtonText: String = "",
    negativeAction: () -> Unit = {}
) {
    MaterialDialog(this)
        .cancelable(true)
        .lifecycleOwner(this)
        .show {
            title(text = title)
            message(text = message)
            positiveButton(text = positiveButtonText) {
                positiveAction.invoke()
            }
            if (negativeButtonText.isNotEmpty()) {
                negativeButton(text = negativeButtonText) {
                    negativeAction.invoke()
                }
            }
            getActionButton(WhichButton.POSITIVE).updateTextColor(SettingUtil.getColor(this@showMessage))
            getActionButton(WhichButton.NEGATIVE).updateTextColor(SettingUtil.getColor(this@showMessage))
        }
}

/**
 * @param message 显示对话框的内容 必填项
 * @param title 显示对话框的标题 默认 温馨提示
 * @param positiveButtonText 确定按钮文字 默认确定
 * @param positiveAction 点击确定按钮触发的方法 默认空方法
 * @param negativeButtonText 取消按钮文字 默认空 不为空时显示该按钮
 * @param negativeAction 点击取消按钮触发的方法 默认空方法
 */
fun Fragment.showMessage(
    message: String,
    title: String = "温馨提示",
    positiveButtonText: String = "确定",
    positiveAction: () -> Unit = {},
    negativeButtonText: String = "",
    negativeAction: () -> Unit = {}
) {
    activity?.let {
        MaterialDialog(it)
            .cancelable(true)
            .lifecycleOwner(viewLifecycleOwner)
            .show {
                title(text = title)
                message(text = message)
                positiveButton(text = positiveButtonText) {
                    positiveAction.invoke()
                }
                if (negativeButtonText.isNotEmpty()) {
                    negativeButton(text = negativeButtonText) {
                        negativeAction.invoke()
                    }
                }
                getActionButton(WhichButton.POSITIVE).updateTextColor(SettingUtil.getColor(it))
                getActionButton(WhichButton.NEGATIVE).updateTextColor(SettingUtil.getColor(it))
            }
    }
}

/**
 * 获取进程号对应的进程名
 *
 * @param pid 进程号
 * @return 进程名
 */
fun getProcessName(pid: Int): String? {
    var reader: BufferedReader? = null
    try {
        reader = BufferedReader(FileReader("/proc/$pid/cmdline"))
        var processName = reader.readLine()
        if (!TextUtils.isEmpty(processName)) {
            processName = processName.trim { it <= ' ' }
        }
        return processName
    } catch (throwable: Throwable) {
        throwable.printStackTrace()
    } finally {
        try {
            reader?.close()
        } catch (exception: IOException) {
            exception.printStackTrace()
        }

    }
    return null
}

/**
 * 拦截登录操作，如果没有登录跳转登录，登录过了则执行你的方法
 */
fun NavController.jumpByLogin(action: (NavController) -> Unit) {
    if (CacheUtil.isLogin()) {
        action(this)
    } else {
//        this.navigateAction(R.id.action_to_loginFragment)
    }
}

/**
 * 拦截登录操作，如果没有登录执行方法 actionLogin 登录过了执行 action
 */
fun NavController.jumpByLogin(
    actionLogin: (NavController) -> Unit,
    action: (NavController) -> Unit
) {
    if (CacheUtil.isLogin()) {
        action(this)
    } else {
        actionLogin(this)
    }
}


fun List<*>?.isNull(): Boolean {
    return this?.isEmpty() ?: true
}

fun List<*>?.isNotNull(): Boolean {
    return this != null && this.isNotEmpty()
}

/**
 * 根据索引获取集合的child值
 * @receiver List<T>?
 * @param position Int
 * @return T?
 */
inline fun <reified T> List<T>?.getChild(position: Int): T? {
    //如果List为null 返回null
    return if (this == null) {
        null
    } else {
        //如果position大于集合的size 返回null
        if (position + 1 > this.size) {
            null
        } else {
            //返回正常数据
            this[position]
        }
    }
}

/**
 * Performs a permission request, asking for all given [permissions], and
 * invoking the [callback] with the result.
 */
fun FragmentActivity.askForPermissions(
    permissions: List<String>,
    callback: RequestCallback?
) {
    PermissionX.init(this)
        .permissions(permissions)
        .request { allGranted, grantedList, deniedList ->
            callback?.onResult(allGranted, grantedList, deniedList)
        }
}

/**
 * Like [askForPermissions], but only executes the [excute] callback if all given
 * [permissions] are granted.
 */
fun FragmentActivity.runWithPermissions(
    permissions: List<String>,
    excute: () -> Unit = {}
) {
    askForPermissions(permissions) { allGranted, _, _ ->
        if (allGranted) {
            excute.invoke()
        }
    }
}

/**
 * Performs a permission request, asking for all given [permissions], and
 * invoking the [callback] with the result.
 */
fun Fragment.askForPermissions(
    permissions: List<String>,
    callback: RequestCallback?
) {
    PermissionX.init(this)
        .permissions(permissions)
        .request { allGranted, grantedList, deniedList ->
            callback?.onResult(allGranted, grantedList, deniedList)
        }
}

/**
 * Like [askForPermissions], but only executes the [excute] callback if all given
 * [permissions] are granted.
 */
fun Fragment.runWithPermissions(
    permissions: List<String>,
    excute: () -> Unit = {}
) {
    askForPermissions(permissions) { allGranted, _, _ ->
        if (allGranted) {
            excute.invoke()
        } else {
            "权限被拒绝,请在应用设置中打开相应权限".toast()
        }
    }
}

fun checkAllPropertiesNotNull(obj: Any): Boolean {
    val properties = obj::class.memberProperties
    for (property in properties) {
        val value = property.getter.call(obj)
        if (value !is String || value.isEmpty()) {
            return false
        }
    }

    return true
}

fun initLoggerConfig(logFilePath: String) {
    val config = LogConfiguration.Builder()
        .logLevel(if (BuildConfig.DEBUG) LogLevel.ALL else LogLevel.WARN)
        .tag("VibePress")
        .enableThreadInfo()
        .enableBorder()
        .enableStackTrace(2)
        .addInterceptor(
            BlacklistTagsFilterInterceptor( // Add blacklist tags filter
                "blacklist1", "blacklist2", "blacklist3"
            )
        )
        .build()

    val filePrinter = FilePrinter.Builder(logFilePath)
        .fileNameGenerator(DateFileNameGenerator())
        .flattener(ClassicFlattener())
        .writer(object : SimpleWriter() {
            override fun onNewFileCreated(file: File?) {
                super.onNewFileCreated(file)
                val header = """
                        >>>>>>>>>>>>>>>> File Header >>>>>>>>>>>>>>>>
                        Device Manufacturer: ${Build.MANUFACTURER}
                        Device Model       : ${Build.MODEL}
                        Android Version    : ${Build.VERSION.RELEASE}
                        Android SDK        : ${Build.VERSION.SDK_INT}
                        App VersionName    : ${BuildConfig.VERSION_NAME}
                        App VersionCode    : ${BuildConfig.VERSION_CODE}
                        <<<<<<<<<<<<<<<< File Header <<<<<<<<<<<<<<<<
                        """.trimIndent()
                appendLog(header)
            }
        })
        .build()

    XLog.init(config, AndroidPrinter(), filePrinter)
}


