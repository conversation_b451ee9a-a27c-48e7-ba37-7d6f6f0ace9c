package com.ztkj.vibepress.app.db

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.Query
import com.ztkj.vibepress.data.model.bean.VibeMetaData
import kotlinx.coroutines.flow.Flow

/**
 * @CreateTime : 2023/5/16 9:50
 * <AUTHOR> AppOS
 * @Description :
 */
@Dao
interface VibeDao {

    /**
     * 查询所有的数据
     */
    @Query("SELECT * FROM VibeMetaData")
    fun getAll(): Flow<List<VibeMetaData>>

    /**
     * 插入数据
     */
    @Insert
    suspend fun insert(vibeMetaData: VibeMetaData)

    @Insert
    suspend fun insertAll(vararg vibeMetaData: VibeMetaData)

    @Delete
    suspend fun delete(vibeMetaData: VibeMetaData)

    @Query("DELETE FROM VibeMetaData")
    suspend fun deleteAll()
}