package com.ztkj.vibepress.app.ext

import com.ztkj.imext.bean.SingleMessage

/**
 * @CreateTime : 2023/5/25 17:36
 * <AUTHOR> AppOS
 * @Description :
 */
enum class MessageStatus {
    SUCCESS,
    FAILED,
    ERROR,
    RESEND,
    NO_RESEND
}

/**
 * 定义一个状态
 * 1: 发送成功
 * 2001X 不重发
 * 2002X 重发
 * 其余默认发送失败
 */
val SingleMessage.messageStatus: MessageStatus
    get() {
        return when (statusReport) {
            0 -> {
                MessageStatus.ERROR
            }

            1 -> {
                MessageStatus.SUCCESS
            }

            in (20010 until 20020) -> {
                MessageStatus.NO_RESEND
            }

            in (20020 until 20030) -> {
                MessageStatus.RESEND
            }

            else -> {
                MessageStatus.FAILED
            }
        }

    }
