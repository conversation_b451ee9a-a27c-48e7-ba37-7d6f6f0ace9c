package com.ztkj.vibepress.app.util

import com.ztkj.vibepress.data.model.bean.CounterParseEntity

/**
 * 传感器数据解析
 */
object CounterParser {

    fun parseCounterData(data: String): CounterParseEntity? {
        if (data.startsWith("\$C")) {
            return parseCData(data)
        }
        return null
    }

    /**
     * 解析$C数据
     * $C,29,1.00,0.00,0,0*65
     */
    private fun parseCData(data: String): CounterParseEntity? {
        val split = data.split(",")
        try {
            return if (split.size >= 8) {
                CounterParseEntity(
                    split[0],
                    split[1],
                    split[2],
                    split[3],
                    split[4].toInt(),
                    split[5].toInt(),
                    split[6].toInt(),
                    split[7].toInt()
                )
            } else CounterParseEntity(
                split[0],
                split[1],
                split[2],
                split[3],
                split[4].toInt()
            )
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return null
    }
}