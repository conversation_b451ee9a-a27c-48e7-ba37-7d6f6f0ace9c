package com.ztkj.vibepress.app.base

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.util.DisplayMetrics
import android.view.Gravity
import android.view.ViewGroup
import androidx.databinding.ViewDataBinding
import com.ztkj.baselib.base.fragment.BaseVmDbDialogFragment
import com.ztkj.baselib.base.viewmodel.BaseViewModel
import com.ztkj.vibepress.app.ext.dismissLoadingExt
import com.ztkj.vibepress.app.ext.hideSoftKeyboard
import com.ztkj.vibepress.app.ext.showLoadingExt

/**
 * @CreateTime : 2022/10/21 10:50
 * <AUTHOR> AppOS
 * @Description :
 */
abstract class BaseBottomSheetFragment<VM : BaseViewModel, VB : ViewDataBinding> : BaseVmDbDialogFragment<VM, VB>() {
    abstract override fun initView(savedInstanceState: Bundle?)

    /**
     * 懒加载 只有当前fragment视图显示时才会触发该方法
     */
    override fun lazyLoadData() {}

    /**
     * 创建LiveData观察者 Fragment执行onViewCreated后触发
     */
    override fun createObserver() {}

    /**
     * Fragment执行onViewCreated后触发
     */
    override fun initData() {

    }

    /**
     * 打开等待框
     */
    override fun showLoading(message: String) {
        showLoadingExt(message)
    }

    /**
     * 关闭等待框
     */
    override fun dismissLoading() {
        dismissLoadingExt()
    }

    override fun onPause() {
        super.onPause()
        hideSoftKeyboard(activity)
    }

    /**
     * 延迟加载 防止 切换动画还没执行完毕时数据就已经加载好了，这时页面会有渲染卡顿  bug
     * 这里传入你想要延迟的时间，延迟时间可以设置比转场动画时间长一点 单位： 毫秒
     * 不传默认 300毫秒
     * @return Long
     */
    override fun lazyLoadTime(): Long {
        return 300
    }

    override fun onStart() {
        super.onStart()
        //点击周边不隐藏对话框
        dialog?.setCanceledOnTouchOutside(false)
        // 设置宽度为屏宽, 靠近屏幕底部。
        val window = dialog?.window
        // 一定要设置Background，如果不设置，window属性设置无效
        window?.run {
            setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            //设置周边透明度
            setDimAmount(0.1f)
        }

        activity?.windowManager?.defaultDisplay?.getMetrics(DisplayMetrics())

        val attributes = window?.attributes
        attributes?.run {
            gravity = Gravity.BOTTOM
            width= ViewGroup.LayoutParams.MATCH_PARENT
            height = ViewGroup.LayoutParams.MATCH_PARENT
            window.attributes = this
        }
    }
}