package com.ztkj.vibepress.app.util

import java.math.BigDecimal
import java.math.RoundingMode
import java.text.DecimalFormat

/**
 * @CreateTime : 2023/9/19 15:09
 * <AUTHOR> AppOS
 * @Description :
 */
object BenchmarkUtil {

    /**
     * 计算标高
     * @param tampingEnergy 夯击能
     * @param hammerWeight  锤重
     * @return 标高值
     */
    fun calculateBenchmarkHeight(tampingEnergy: BigDecimal, hammerWeight: Double): Float {
        val gravity = 9.81f // m/s²
        val result = tampingEnergy.toFloat() / (hammerWeight.toFloat() * gravity)
        val decimalFormat = DecimalFormat("#.##")
        return decimalFormat.format(result).toFloat()
    }

}