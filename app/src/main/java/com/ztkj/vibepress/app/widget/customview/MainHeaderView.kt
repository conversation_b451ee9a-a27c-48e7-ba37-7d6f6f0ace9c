package com.ztkj.vibepress.app.widget.customview

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.cardview.widget.CardView
import com.ztkj.baselib.ext.util.setOnclickNoRepeat
import com.ztkj.vibepress.databinding.LayoutMainHeaderViewBinding

/**
 * @CreateTime : 2023/6/8 11:25
 * <AUTHOR> AppOS
 * @Description :
 */
class MainHeaderView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : CardView(context, attrs, defStyleAttr) {

    private val binding: LayoutMainHeaderViewBinding

    init {
        binding = LayoutMainHeaderViewBinding.inflate(LayoutInflater.from(context), this, true)
        attrs?.let { applyAttributes(it) }
    }

    private fun applyAttributes(attrs: AttributeSet) {
        /*      val typedArray = context.obtainStyledAttributes(attrs, R.styleable.VibeHeaderView)
              val centerTitle = typedArray.getString(R.styleable.VibeHeaderView_centerTitle)
              val showSaveIcon = typedArray.getBoolean(R.styleable.VibeHeaderView_showSaveIcon, false)
              binding.save.setCustomVisibility(showSaveIcon)
              typedArray.recycle()

              binding.centerTitle.text = centerTitle*/
    }

    fun setSerialNumber(value: String) {
        binding.serialNumber.text = value
    }

    /**
     * 设置传感器的状态值
     */
    fun setTension(tensionValue: String?, tensionUnit: String?) {
        binding.tvTensionValue.text = tensionValue
        binding.tvTensionStatus.text = tensionUnit
    }

    /**
     * 设置GPS的状态值
     */
    fun setGpsInfo(gpsStatus: String?, tvSatelliteNumber: String?) {
        binding.tvGpsStatus.text = gpsStatus
        binding.tvSatelliteNumber.text = tvSatelliteNumber
    }

    fun onBack(onClick: (View) -> Unit) {
        /*        setOnclickNoRepeat(binding.back) {
                    onClick.invoke(it)
                }*/
    }

    fun settingClick(onClick: (View) -> Unit) {
        setOnclickNoRepeat(binding.setting) {
            onClick.invoke(it)
        }
    }

}