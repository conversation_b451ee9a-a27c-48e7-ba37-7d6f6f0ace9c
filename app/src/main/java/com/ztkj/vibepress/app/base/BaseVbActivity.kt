package com.ztkj.vibepress.app.base

import android.os.Bundle
import androidx.viewbinding.ViewBinding
import com.ztkj.baselib.base.activity.BaseVmVbActivity
import com.ztkj.baselib.base.viewmodel.BaseViewModel
import com.ztkj.vibepress.app.ext.dismissLoadingExt
import com.ztkj.vibepress.app.ext.showLoadingExt

/**
 * @CreateTime : 2022/10/12 14:37
 * <AUTHOR> AppOS
 * @Description : 项目中的Activity基类，在这里实现显示弹窗，吐司，还有加入自己的需求操作
 */
abstract class BaseVbActivity<VM : BaseViewModel, VB : ViewBinding> : BaseVmVbActivity<VM, VB>() {

    abstract override fun initView(savedInstanceState: Bundle?)

    /**
     * 创建liveData观察者
     */
    override fun createObserver() {}

    /**
     * 打开等待框
     */
    override fun showLoading(message: String) {
        showLoadingExt(message)
    }

    /**
     * 关闭等待框
     */
    override fun dismissLoading() {
        dismissLoadingExt()
    }

    /* *//**
     * 在任何情况下本来适配正常的布局突然出现适配失效，适配异常等问题，只要重写 Activity 的 getResources() 方法
     *//*
    override fun getResources(): Resources {
        AutoSizeCompat.autoConvertDensityOfGlobal(super.getResources())
        return super.getResources()
    }*/
}