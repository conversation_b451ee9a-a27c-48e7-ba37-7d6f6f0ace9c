package com.ztkj.vibepress

import android.content.Context
import android.text.TextUtils
import androidx.multidex.MultiDex
import cat.ereza.customactivityoncrash.config.CaocConfig
import com.kingja.loadsir.callback.SuccessCallback
import com.kingja.loadsir.core.LoadSir
import com.tencent.bugly.crashreport.CrashReport
import com.ztkj.app.mapcore.util.TianDiTuTiledLayerClass
import com.ztkj.baselib.base.BaseApp
import com.ztkj.vibepress.app.event.AppViewModel
import com.ztkj.vibepress.app.event.EventViewModel
import com.ztkj.vibepress.app.ext.initLoggerConfig
import com.ztkj.vibepress.app.util.SpUtils
import com.ztkj.vibepress.app.widget.callback.EmptyCallback
import com.ztkj.vibepress.app.widget.callback.ErrorCallback
import com.ztkj.vibepress.app.widget.callback.LoadingCallback
import com.ztkj.vibepress.data.Constants
import com.ztkj.vibepress.ui.activity.ErrorActivity
import com.ztkj.vibepress.ui.activity.SplashActivity
import dagger.hilt.android.HiltAndroidApp
import java.io.BufferedReader
import java.io.File
import java.io.FileReader
import java.io.IOException

//Application全局的ViewModel，里面存放了一些账户信息，基本配置信息等
val appViewModel: AppViewModel by lazy { App.appViewModelInstance }

//Application全局的ViewModel，用于发送全局通知操作
val eventViewModel: EventViewModel by lazy { App.eventViewModelInstance }

/**
 * @CreateTime : 2023/4/19 8:36
 * <AUTHOR> AppOS
 * @Description :
 */
@HiltAndroidApp
class App : BaseApp() {

    companion object {
        lateinit var instance: App
        lateinit var eventViewModelInstance: EventViewModel
        lateinit var appViewModelInstance: AppViewModel
    }

    override fun attachBaseContext(base: Context?) {
        super.attachBaseContext(base)
        MultiDex.install(this)
    }

    override fun onCreate() {
        super.onCreate()
        //初始化SharedPreference，里面包含了MMKV的初始化
        SpUtils.init(this)
        instance = this
        //初始化天地图key
        TianDiTuTiledLayerClass.initTianDiTuKey(Constants.MAP.TDT_MAP_KEY)
        eventViewModelInstance = getAppViewModelProvider()[EventViewModel::class.java]
        appViewModelInstance = getAppViewModelProvider()[AppViewModel::class.java]
//        MultiDex.install(this)
        //界面加载管理 初始化
        LoadSir.beginBuilder()
            .addCallback(LoadingCallback())//加载
            .addCallback(ErrorCallback())//错误
            .addCallback(EmptyCallback())//空
            .setDefaultCallback(SuccessCallback::class.java)//设置默认加载状态页
            .commit()

        //Tencent Bugly
        val context = applicationContext
        // 获取当前包名
        val packageName = context.packageName
        // 获取当前进程名
        val processName = getProcessName(android.os.Process.myPid())
        val userStrategy = CrashReport.UserStrategy(applicationContext)
        userStrategy.isUploadProcess = processName == null || processName == packageName
        CrashReport.initCrashReport(this, "7bd15b6e0a", BuildConfig.DEBUG)

        //防止项目崩溃，崩溃后打开错误界面
        CaocConfig.Builder.create()
            .backgroundMode(CaocConfig.BACKGROUND_MODE_SILENT) //default: CaocConfig.BACKGROUND_MODE_SHOW_CUSTOM
            .enabled(true)//是否启用CustomActivityOnCrash崩溃拦截机制 必须启用！不然集成这个库干啥？？？
            .showErrorDetails(false) //是否必须显示包含错误详细信息的按钮 default: true
            .showRestartButton(true) //是否必须显示“重新启动应用程序”按钮或“关闭应用程序”按钮default: true
            .logErrorOnRestart(false) //是否必须重新堆栈堆栈跟踪 default: true
            .trackActivities(true) //是否必须跟踪用户访问的活动及其生命周期调用 default: false
            .minTimeBetweenCrashesMs(3000) //应用程序崩溃之间必须经过的时间 default: 3000
            .restartActivity(SplashActivity::class.java) // 重启的activity
            .errorActivity(ErrorActivity::class.java) //发生错误跳转的activity
            .apply()

        val logFilePath = File(externalCacheDir?.absolutePath, "log").path
        initLoggerConfig(logFilePath)

        /*        val displayMetrics = resources.displayMetrics
                val screenWidth = displayMetrics.widthPixels
                val screenHeight = displayMetrics.heightPixels
                println("屏幕的分辨率：${screenWidth}*${screenHeight}")
                val resourceId = context.resources.getIdentifier("status_bar_height", "dimen", "android")
                val dimensionPixelSize = context.resources.getDimensionPixelSize(resourceId)
                println("屏幕 状态栏高度: $dimensionPixelSize")
                val navigationRes = resources.getIdentifier("navigation_bar_height", "dimen", "android")
                println("屏幕 导航栏高度: ${resources.getDimension(navigationRes)}")*/

    }


    /**
     * 获取进程号对应的进程名
     *
     * @param pid 进程号
     * @return 进程名
     */
    private fun getProcessName(pid: Int): String? {
        var reader: BufferedReader? = null
        try {
            reader = BufferedReader(FileReader("/proc/$pid/cmdline"))
            var processName = reader.readLine()
            if (!TextUtils.isEmpty(processName)) {
                processName = processName.trim { it <= ' ' }
            }
            return processName
        } catch (throwable: Throwable) {
            throwable.printStackTrace()
        } finally {
            try {
                reader?.close()
            } catch (exception: IOException) {
                exception.printStackTrace()
            }

        }
        return null
    }
}