package com.ztkj.vibepress.viewmodel.state

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.ztkj.baselib.base.viewmodel.BaseViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class CounterStateViewModel : BaseViewModel() {

    // 计时器模式：true为正数模式，false为倒数模式
    val isCountUpMode = MutableLiveData(true)

    // 计时器状态：true为运行中，false为停止
    val isRunning = MutableLiveData(false)

    // 当前时间显示（秒）
    val currentTime = MutableLiveData(0L)

    // 倒计时设置时间（秒）
    val countdownDuration = MutableLiveData(60L)

    // 倒计时分钟输入
    val countdownMinutes = MutableLiveData("1")

    // 倒计时秒数输入
    val countdownSeconds = MutableLiveData("0")

    // 计时器任务
    private var timerJob: Job? = null

    // 开始时间戳
    private var startTime = 0L

    /**
     * 切换计时器模式
     */
    fun toggleMode() {
        if (isRunning.value == true) {
            stopTimer()
        }
        isCountUpMode.value = !(isCountUpMode.value ?: true)
        resetTimer()
    }

    /**
     * 开始计时器
     */
    fun startTimer() {
        if (isRunning.value == true) return

        isRunning.value = true
        startTime = System.currentTimeMillis()

        // 模拟调用后台接口
        simulateApiCall("计时器开始")

        timerJob = viewModelScope.launch {
            while (isRunning.value == true) {
                delay(100) // 每100ms更新一次，提供更流畅的显示
                updateTime()
            }
        }
    }

    /**
     * 停止计时器
     */
    fun stopTimer() {
        if (isRunning.value == false) return

        isRunning.value = false
        timerJob?.cancel()
        timerJob = null

        // 模拟调用后台接口
        simulateApiCall("计时器停止")
    }

    /**
     * 重置计时器
     */
    fun resetTimer() {
        stopTimer()
        currentTime.value = if (isCountUpMode.value == true) 0L else countdownDuration.value
    }

    /**
     * 设置倒计时时长（分钟和秒）
     */
    fun setCountdownDuration(minutes: String, seconds: String) {
        try {
            val mins = minutes.toIntOrNull() ?: 0
            val secs = seconds.toIntOrNull() ?: 0
            val totalSeconds = (mins * 60 + secs).toLong()

            // 确保至少有1秒
            val duration = if (totalSeconds <= 0) 1L else totalSeconds

            countdownDuration.value = duration
            if (isCountUpMode.value == false) {
                currentTime.value = duration
            }
        } catch (e: Exception) {
            // 输入无效时使用默认值
            countdownDuration.value = 60L
            if (isCountUpMode.value == false) {
                currentTime.value = 60L
            }
        }
    }

    /**
     * 当分钟输入改变时自动更新倒计时时长
     */
    fun onMinutesChanged(text: CharSequence) {
        val minutes = text.toString()
        countdownMinutes.value = minutes
        setCountdownDuration(minutes, countdownSeconds.value ?: "0")
    }

    /**
     * 当秒数输入改变时自动更新倒计时时长
     */
    fun onSecondsChanged(text: CharSequence) {
        val seconds = text.toString()
        countdownSeconds.value = seconds
        setCountdownDuration(countdownMinutes.value ?: "0", seconds)
    }

    /**
     * 更新时间显示
     */
    private fun updateTime() {
        val elapsed = (System.currentTimeMillis() - startTime) / 1000

        if (isCountUpMode.value == true) {
            // 正数模式：从0开始递增
            currentTime.postValue(elapsed)
        } else {
            // 倒数模式：从设定时间开始递减
            val remaining = (countdownDuration.value ?: 60L) - elapsed
            if (remaining <= 0) {
                // 倒计时结束
                currentTime.postValue(0L)
                stopTimer()
                simulateApiCall("倒计时结束")
            } else {
                currentTime.postValue(remaining)
            }
        }
    }

    /**
     * 模拟API调用
     */
    private fun simulateApiCall(message: String) {
        // 这里使用ViewModel的扩展方法来显示Toast
        // 在实际项目中，您可以通过LiveData通知Fragment显示Toast
        viewModelScope.launch {
            // 模拟网络延迟
            delay(100)
            // 通过LiveData通知UI显示Toast
            _toastMessage.postValue(message)
        }
    }

    // 用于显示Toast消息的LiveData
    private val _toastMessage = MutableLiveData<String>()
    val toastMessage: MutableLiveData<String> = _toastMessage

    /**
     * 格式化时间显示
     */
    fun formatTime(seconds: Long): String {
        val hours = seconds / 3600
        val minutes = (seconds % 3600) / 60
        val secs = seconds % 60

        return if (hours > 0) {
            String.format("%02d:%02d:%02d", hours, minutes, secs)
        } else {
            String.format("%02d:%02d", minutes, secs)
        }
    }

    override fun onCleared() {
        super.onCleared()
        timerJob?.cancel()
    }
}