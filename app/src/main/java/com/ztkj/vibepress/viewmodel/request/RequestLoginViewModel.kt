package com.ztkj.vibepress.viewmodel.request

import androidx.lifecycle.MutableLiveData
import com.ztkj.baselib.base.viewmodel.BaseViewModel
import com.ztkj.baselib.ext.requestNoCheck
import com.ztkj.baselib.state.ResultState
import com.ztkj.vibepress.app.network.apiService
import com.ztkj.vibepress.data.model.bean.LoginClient

/**
 * @CreateTime : 2023/4/24 14:29
 * <AUTHOR> AppOS
 * @Description :
 */
class RequestLoginViewModel: BaseViewModel() {
    var loginResult = MutableLiveData<ResultState<LoginClient>>()

    /**
     * 登录
     */
    fun login(userName: String, password: String) {
        requestNoCheck(
            { apiService.login(userName,password)},
            loginResult,
            true,
            "正在登录中..."
        )
    }
}