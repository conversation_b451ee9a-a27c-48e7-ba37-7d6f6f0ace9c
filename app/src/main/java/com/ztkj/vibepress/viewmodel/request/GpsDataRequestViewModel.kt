package com.ztkj.vibepress.viewmodel.request

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.ztkj.baselib.base.viewmodel.BaseViewModel
import com.ztkj.baselib.ext.request
import com.ztkj.baselib.state.ResultState
import com.ztkj.vibepress.app.network.apiService
import com.ztkj.vibepress.data.model.bean.TampGpsDetailEntity
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

/**
 * GPS数据请求ViewModel
 * @CreateTime : 2025/07/31
 * <AUTHOR> AppOS
 * @Description : 处理GPS数据的网络请求和循环调用
 */
class GpsDataRequestViewModel : BaseViewModel() {

    /**
     * GPS数据查询结果
     */
    var gpsDataResult = MutableLiveData<ResultState<TampGpsDetailEntity>>()

    /**
     * 循环请求的Job
     */
    private var periodicJob: Job? = null

    /**
     * 获取GPS数据详情
     * @param deviceId 设备ID
     */
    fun getTampGpsData(deviceId: String) {
        request(
            { apiService.getTampGpsDataById(deviceId) },
            gpsDataResult,
            false
        )
    }

    /**
     * 开始循环获取GPS数据
     * @param deviceId 设备ID
     * @param intervalMillis 间隔时间（毫秒），默认3秒
     */
    fun startPeriodicGpsDataRequest(deviceId: String, intervalMillis: Long = 3000L) {
        // 先停止之前的循环请求
        stopPeriodicGpsDataRequest()
        
        periodicJob = viewModelScope.launch {
            while (isActive) {
                try {
                    // 发起网络请求
                    getTampGpsData(deviceId)
                    // 等待指定时间间隔
                    delay(intervalMillis)
                } catch (e: Exception) {
                    e.printStackTrace()
                    // 即使出现异常也继续循环，但可以考虑增加重试逻辑
                    delay(intervalMillis)
                }
            }
        }
    }

    /**
     * 停止循环获取GPS数据
     */
    fun stopPeriodicGpsDataRequest() {
        periodicJob?.cancel()
        periodicJob = null
    }

    /**
     * ViewModel销毁时停止循环请求
     */
    override fun onCleared() {
        super.onCleared()
        stopPeriodicGpsDataRequest()
    }
}
