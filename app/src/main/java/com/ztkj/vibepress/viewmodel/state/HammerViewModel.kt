package com.ztkj.vibepress.viewmodel.state

import androidx.lifecycle.ViewModel
import com.ztkj.vibepress.data.model.bean.JobStandardEntity
import com.ztkj.vibepress.data.model.bean.TampDeviceEntity
import com.ztkj.vibepress.data.reposity.JobStandardRepository
import com.ztkj.vibepress.data.reposity.TampDeviceRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * @CreateTime : 2023/7/11 10:43
 * <AUTHOR> AppOS
 * @Description :
 */
@HiltViewModel
class HammerViewModel @Inject constructor(
    private val repository: JobStandardRepository,
    private val tampDeviceRepository: TampDeviceRepository
) :
    ViewModel() {

    fun getJobStandardList(): Flow<List<JobStandardEntity>> {
        return repository.getJobStandardList()
    }

    fun getTampDeviceEntityFlow(): Flow<TampDeviceEntity?> {
        return tampDeviceRepository.getTampEntityFlow()
    }


    /*    fun updateTampDeviceEntity(radius: Double, weight: Double) {
            viewModelScope.launch(Dispatchers.IO) {
                val tampEntity = tampDeviceRepository.getTampEntity()
                if (tampEntity != null) {
                    tampEntity.hammerRadius = radius
                    tampEntity.hammerWeight = weight
                    tampDeviceRepository.insert(tampEntity)
                    tampDeviceRepository.updateTampDeviceEntity(tampEntity)
                }
            }
        }*/


}