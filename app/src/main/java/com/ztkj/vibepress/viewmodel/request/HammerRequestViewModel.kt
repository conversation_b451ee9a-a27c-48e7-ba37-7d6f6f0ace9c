package com.ztkj.vibepress.viewmodel.request

import androidx.lifecycle.MutableLiveData
import com.ztkj.baselib.base.viewmodel.BaseViewModel
import com.ztkj.baselib.ext.request
import com.ztkj.baselib.state.ResultState
import com.ztkj.vibepress.app.network.apiService
import com.ztkj.vibepress.data.model.bean.ProcessEntity

class HammerRequestViewModel : BaseViewModel() {

    var processEntityResult = MutableLiveData<ResultState<List<ProcessEntity>>>()

    fun getHjProcessEntity(deviceId: String?) {
        request(
            { apiService.getHjProcessProperty(deviceId) },
            processEntityResult,
            false
        )
    }
}