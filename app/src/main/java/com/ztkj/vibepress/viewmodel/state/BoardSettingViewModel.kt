package com.ztkj.vibepress.viewmodel.state

import androidx.lifecycle.MutableLiveData
import com.ztkj.baselib.base.viewmodel.BaseViewModel
import com.ztkj.vibepress.data.model.kEnum.ServiceDataType

class BoardSettingViewModel: BaseViewModel() {

    var boardType = MutableLiveData(ServiceDataType.GNSS_ALL)

    /**
     * GNSS传感器数据
     */
    var gnssValue = MutableLiveData<String>()

    /**
     * 计数器传感器数据
     */
    var counterValue = MutableLiveData<String>()

    var showLatestData: Boolean = false
}