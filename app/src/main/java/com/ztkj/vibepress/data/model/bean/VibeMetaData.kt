package com.ztkj.vibepress.data.model.bean

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * @CreateTime : 2023/5/16 9:28
 * <AUTHOR> AppOS
 * @Description :
 */
@Entity
data class VibeMetaData(
    @PrimaryKey(autoGenerate = true) val mid: Long = 0, //数据ID
    @ColumnInfo(name = "body") val body: String, //RTK传感器的数据 + 夯机获取的数据
    @ColumnInfo(name = "timestamp") val timestamp: Long = System.currentTimeMillis() //时间戳
)
