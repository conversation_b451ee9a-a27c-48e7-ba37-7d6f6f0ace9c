package com.ztkj.vibepress.data

/**
 * @CreateTime : 2023/4/23 9:24
 * <AUTHOR> AppOS
 * @Description :
 */
class Constants {

    internal interface CONFIG {
        companion object {
            /**
             * 网络参数
             */
            const val NETWORK_CONFIG = "NETWORK_CONFIG"

            /**
             * 串口参数
             */
            const val SERIAL_PORT_CONFIG = "SERIAL_PORT_CONFIG"

            const val HAMMER_HEIGHT_RATIO = "HAMMER_HEIGHT_RATIO"

            const val APP_TYPE = "APP_TYPE"

            const val CURRENT_JOB_STANDARD_ENTITY = "CURRENT_JOB_STANDARD_ENTITY"

            const val CURRENT_TAMP_DEVICE_ENTITY = "CURRENT_TAMP_DEVICE_ENTITY"

            const val ADMIN_SETTING_PASSWORD = "ztkj"
        }
    }

    internal interface USER {
        companion object {
            const val USER_INFO = "USER_INFO"
            const val LOCAL_SEARCH_HISTORY = "LOCAL_SEARCH_HISTORY"
        }
    }

    internal interface DEVICE {
        companion object {
            const val DEFAULT_TENSION = "DEFAULT_TENSION"
        }
    }

    internal interface BLADE_X {
        companion object {
            const val TENANT_ID = "000000"
            const val SCOPE = "all"
            const val GRANT_TYPE_PASSWORD = "GRANT_TYPE_PASSWORD"
            const val GRANT_TYPE_REFRESH_TOKEN = "refresh_token"
            const val BLADE_CURRENT_USER = "BLADE_CURRENT_USER"
        }
    }

    internal interface LOGIN {
        companion object {
            const val IS_LOGIN = "IS_LOGIN"
            const val ACCESS_TOKEN = "ACCESS_TOKEN"
            const val REFRESH_TOKEN = "REFRESH_TOKEN"
        }
    }

    internal interface MAP {
        companion object {
            //天地图key
            const val TDT_MAP_KEY = "53084cd0c2deaeb8a7b1880807e4727f"

            // WFS 夯点图层
            //const val WFS_TAMPING_URL =
            //"http://192.168.1.154:9709/geoserver/jc/ows?service=WFS&version=2.0.0&request=GetCapabilities"
            const val WFS_TAMPING_URL =
                "http://218.77.59.2:9709/geoserver/jc/ows?service=WFS&version=2.0.0&request=GetCapabilities"
            const val WFS_TAMPING_TABLE_PREFIX = "jc:pt_tamp_stake_"

            const val WMS_DESIGN_POINT_URL =
                "http://218.77.59.2:9709/geoserver/ows?service=wms&version=1.3.0&request=GetCapabilities"
            const val WMS_DESIGN_POINT_TABLE_NAME = "jc:pt_tamp_design_point"

        }
    }

    internal interface NAVIGATION_PARAMS {
        companion object {
            const val JOB_STANDARD_LIST_TO_DETAIL = "JOB_STANDARD_LIST_TO_DETAIL"
            const val JOB_WORK_TYPE = "JOB_WORK_TYPE"
        }
    }

    internal interface TAMP {

        companion object {
            /**
             * 上升 锤状态
             */
            const val HAMMER_STATE_UP = 1

            /**
             * 下降 锤状态
             */
            const val HAMMER_STATE_DOWN = 2

            /**
             * 波峰 锤状态
             */
            const val HAMMER_STATE_PEAK = 3

            /**
             * 波谷 锤状态
             */
            const val HAMMER_STATE_TROUGH = 4

            /**
             * 有张力
             */
            const val TENSION_STATE_HAS = 1

            /**
             * 无张力
             */
            const val TENSION_STATE_NO = 0

            /**
             * 无变化 张力变化事件
             */
            const val TENSION_CHANGE_NO = 0

            /**
             * 提锤 张力变化事件
             */
            const val TENSION_CHANGE_LIFT = 1

            /**
             * 放锤 张力变化事件
             */
            const val TENSION_CHANGE_RELEASE = 2

            /**
             * 张力状态判断的最小值
             * value < MIN_TENSION_VALUE 张力状态异常 0
             * MIN_TENSION_VALUE < value < 张力阈值   无张力 1
             * value > 张力阈值 有张力 2
             */
            const val MIN_TENSION_VALUE = 0.4f
        }

    }

    internal interface UPDATE {
        companion object {
            /**
             * App更新地址
             */
            const val APP_UPDATE_URL = "http://218.77.59.2:6212/android/dcm/app/update.json"
        }
    }

    /**
     * 传感器命令
     */
    internal interface SENSOR_COMMAND {
        companion object {
            /**
             * read
             */
            const val COMMAND_READ = "read cfg \r\n"
        }
    }

}