package com.ztkj.vibepress.data.model.bean

/**
 * 传感器解析实体类
 */
data class CounterParseEntity(
    val prefix: String,
    val counterValue: String,
    /**
     * 数据上报频率(Hz)
     */
    val frequency: String,
    /**
     * 张力传感器阈值
     */
    val tensionThreshold: String,
    /**
     * 是否每次开机计数器自动复位，0：继续上次断电的数值；1：每次开机从0开始
     */
    val zero: Int = 0,
    /**
     * 计数器换向，0：不换向；1：换向
     */
    val counterReversing: Int = 0,
    /**
     *外部GPS设置
     */
    val gpsSetting: Int = 0,
    /**
     * 外部差分设置
     */
    val diffSetting: Int = 0
)
