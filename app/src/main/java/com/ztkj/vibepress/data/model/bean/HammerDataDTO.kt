package com.ztkj.vibepress.data.model.bean

/**
 * @CreateTime : 2023/5/11 14:49
 * <AUTHOR> AppOS
 * @Description :
 */
data class HammerDataDTO(
    var sensorValue: Int = 0, //计数器值
    var hammerHeight: Double = 0.0, //锤高
    var hammerState: Int = 0, //锤状态(1:上升；2：下降；3：波峰；4：波谷)
    var tensionValue: Float = 0f, //张力原始值
    var tensionState: Int = 0, //张力状态（0:无张力；1:有张力）
    var tensionChange: Int = 0, //张力变化事件（0：无变化；1：提锤；2：放锤）
    //TODO 8月28日 需要将锤高系数发给后台
//    var calibration: Float = 1.0f //锤高系数
)
