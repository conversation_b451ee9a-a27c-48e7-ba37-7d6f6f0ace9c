package com.ztkj.vibepress.data.model.bean

import android.os.Parcelable
import com.ztkj.vibepress.app.ext.checkAllPropertiesNotNull
import kotlinx.parcelize.Parcelize
import java.io.Serializable

/**
 * @CreateTime : 2023/6/13 15:30
 * <AUTHOR> AppOS
 * @Description :
 */
@Parcelize
data class NetConfigBean(
    /**
     * 差分服务器Host
     */
    var diffHost: String,
    /**
     * 差分服务器IP
     */
    var diffIP: String,
    /**
     * 数据中心Host
     */
    var dataCenterHost: String,
    /**
     * 数据中心IP
     */
    var dataCenterIP: String,
    /**
     * 数据对接平台
     */
    var dockingPlatform: String,
    /**
     * 数据对接Host
     */
    var dockingHost: String,
    /**
     * 数据对接IP
     */
    var dockingIP: String
) : Parcelable, Serializable {

    fun isValid() = checkAllPropertiesNotNull(this)
}
