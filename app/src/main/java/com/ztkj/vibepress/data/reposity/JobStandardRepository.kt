package com.ztkj.vibepress.data.reposity

import com.ztkj.vibepress.app.db.JobStandardDao
import com.ztkj.vibepress.data.model.bean.JobStandardEntity
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

/**
 * @CreateTime : 2023/6/19 16:49
 * <AUTHOR> AppOS
 * @Description :
 */
class JobStandardRepository @Inject constructor(private val jobStandardDao: JobStandardDao) {

    suspend fun insert(jobStandardEntity: JobStandardEntity): Long {
        return jobStandardDao.insert(jobStandardEntity)
    }

    suspend fun insertAll(entities: List<JobStandardEntity>) {
        jobStandardDao.insertAll(entities)
    }

    suspend fun getJobStandardByUid(uID: Long): JobStandardEntity? {
        return jobStandardDao.getJobStandardByUid(uID)
    }

    suspend fun deleteJobStandardByUid(uID: Long) {
        jobStandardDao.deleteByUid(uID)
    }

    fun getJobStandardList(): Flow<List<JobStandardEntity>> {
        return jobStandardDao.getJobStandardList()
    }

    fun getJobStandardListByType(workType: Int): List<JobStandardEntity> {
        return jobStandardDao.getJobStandardsByWorkType(workType)
    }

    suspend fun deleteAll() {
        jobStandardDao.deleteAll()
    }


}