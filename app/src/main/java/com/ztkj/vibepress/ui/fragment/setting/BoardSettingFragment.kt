package com.ztkj.vibepress.ui.fragment.setting

import android.content.ComponentName
import android.content.Intent
import android.content.ServiceConnection
import android.os.Bundle
import android.os.IBinder
import com.ztkj.vibepress.R
import com.ztkj.vibepress.app.base.BaseFragment
import com.ztkj.vibepress.app.ext.hideSoftKeyboard
import com.ztkj.vibepress.app.ext.initNormalClose
import com.ztkj.vibepress.app.widget.recyclerview.SpaceItemDecoration
import com.ztkj.vibepress.data.model.kEnum.ServiceDataType
import com.ztkj.vibepress.data.model.kEnum.ServiceDataType.GNSS_ALL
import com.ztkj.vibepress.data.model.kEnum.ServiceDataType.SENSOR_DATA
import com.ztkj.vibepress.databinding.FragmentBoardSettingBinding
import com.ztkj.vibepress.service.BackgroundService
import com.ztkj.vibepress.ui.adapter.SimpleAdapter
import com.ztkj.vibepress.viewmodel.state.BoardSettingViewModel
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class BoardSettingFragment : BaseFragment<BoardSettingViewModel, FragmentBoardSettingBinding>() {

    @Inject
    lateinit var simpleAdapter: SimpleAdapter

    private var boundService: BackgroundService? = null


    override fun initView(savedInstanceState: Bundle?) {
        binding.click = ProxyClick()

        initToolbar()
        initRecyclerView()
        addRadioButtonListener()
    }

    private fun initToolbar() {
        binding.toolbar.initNormalClose(this@BoardSettingFragment)
    }

    private fun addRadioButtonListener() {
        binding.radioGroup.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                R.id.radio_button_1 -> {
                    mViewModel.boardType.value = GNSS_ALL
                }

                R.id.radio_button_2 -> {
                    mViewModel.boardType.value = SENSOR_DATA
                }

                else -> throw IllegalArgumentException("UnKnown checkedId")
            }
        }
    }

    override fun lazyLoadData() {
        super.lazyLoadData()
        bindService()
    }

    /**
     * 这里只需要两种传感器获取到的数据即可
     */
    private fun handleServiceData(serviceDataType: ServiceDataType, data: String) {
        if (serviceDataType == GNSS_ALL) {
            mViewModel.gnssValue.postValue(data)
        } else if (serviceDataType == SENSOR_DATA) {
            mViewModel.counterValue.postValue(data)
        }
    }

    private fun bindService() {
        val intent = Intent(requireContext(), BackgroundService::class.java)
        requireContext().bindService(intent, mServiceConnection, 0)
    }

    private fun unBindService() {
        boundService?.removeOnDataChangedCallback()
        boundService = null
        requireContext().unbindService(mServiceConnection)
    }


    private fun initRecyclerView() {
        binding.rvData.run {
            simpleAdapter.animationEnable = false
            adapter = simpleAdapter
            addItemDecoration(SpaceItemDecoration(4, 2, true))
        }
    }

    override fun createObserver() {
        super.createObserver()
        mViewModel.run {
            boardType.observe(viewLifecycleOwner) {
                when (it) {
                    GNSS_ALL -> {}
                    SENSOR_DATA -> {}
                    else -> throw IllegalArgumentException("Unknown KBoardType")
                }
            }

            gnssValue.observe(viewLifecycleOwner) {
                if (boardType.value == GNSS_ALL) {
                    simpleAdapter.add(it)
                    checkNeedToShowLatest()
                }
            }
            counterValue.observe(viewLifecycleOwner) {
                if (boardType.value == SENSOR_DATA) {
                    simpleAdapter.add(it)
                    checkNeedToShowLatest()
                }
            }
        }

    }

    private fun checkNeedToShowLatest() {
        if (mViewModel.showLatestData && simpleAdapter.itemCount > 1) {
            binding.rvData.smoothScrollToPosition(simpleAdapter.itemCount - 1)
        }
    }

    private fun doAfterSendCommandClick() {
        hideSoftKeyboard(requireActivity())
        binding.btSendCommand.requestFocus()
        binding.etCommand.clearFocus()
    }

    private val mServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            // 这里是与 Service 连接成功时的回调方法
            // 在该回调方法中，可以获取对 Service 的引用，并调用其公共方法
            service?.run {
                // 获取 boundService 实例
                boundService = (service as BackgroundService.MyBinder).getService()
                boundService?.setOnDataChangedCallback { serviceDataType, data ->
                    handleServiceData(serviceDataType, data)
                }
            }

        }

        override fun onServiceDisconnected(name: ComponentName?) {
            // 这里是与 Service 断开连接时的回调方法
        }

    }

    override fun onDestroy() {
        super.onDestroy()
        unBindService()
    }

    inner class ProxyClick {
        fun clearReceive() {
            simpleAdapter.submitList(null)
        }

        fun clearSend() {
            binding.etCommand.setText("")
        }

        fun sendCommand() {
            val command = binding.etCommand.text.toString() + "\r\n"
            if (command.isNotBlank()) {
                boundService?.receiveCommand(mViewModel.boardType.value!!, command)
            }
            doAfterSendCommandClick()
        }

        fun autoLoad() {
            mViewModel.showLatestData = !mViewModel.showLatestData
        }
    }

}