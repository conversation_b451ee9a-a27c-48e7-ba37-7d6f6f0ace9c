package com.ztkj.vibepress.ui.activity

import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.os.Debug
import android.util.Log
import androidx.activity.OnBackPressedCallback
import androidx.navigation.Navigation
import com.elvishew.xlog.XLog
import com.ztkj.baselib.base.viewmodel.BaseViewModel
import com.ztkj.baselib.ext.util.toJson
import com.ztkj.sensorlib.utils.SerialPortUtils
import com.ztkj.vibepress.R
import com.ztkj.vibepress.app.base.BaseVbActivity
import com.ztkj.vibepress.app.ext.toast
import com.ztkj.vibepress.databinding.ActivityMainBinding
import com.ztkj.vibepress.service.BackgroundService
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MainActivity : BaseVbActivity<BaseViewModel, ActivityMainBinding>() {

    private var exitTime = 0L

    override fun initView(savedInstanceState: Bundle?) {
//        window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
        window.statusBarColor = Color.TRANSPARENT
        // 隐藏状态栏
//        window.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)

        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                val nav = Navigation.findNavController(this@MainActivity, R.id.host_fragment)
                if (nav.currentDestination != null && nav.currentDestination!!.id != R.id.homeFragment) {
                    //如果当前界面不是主页，那么直接调用返回即可
                    nav.navigateUp()
                } else {
                    //是主页
                    if (System.currentTimeMillis() - exitTime > 2000) {
                        getString(R.string.confirm_exit_app).toast()
                        exitTime = System.currentTimeMillis()
                    } else {
                        finish()
                    }
                }
            }
        })

        val maxHeapSize = Debug.getNativeHeapSize() / (1024 * 1024) // 转换为 MB
        val maxFreeHeapSize = Debug.getNativeHeapFreeSize() / (1024 * 1024)
        Log.e("TAG", "initView: ${maxHeapSize}MB,${maxFreeHeapSize}MB")

        //TODO 启动服务，先屏蔽
//        startService(Intent(this, BackgroundService::class.java))

        XLog.i("串口列表: ${SerialPortUtils.getAllDevicesPath().toJson()}")
    }

    override fun onDestroy() {
        super.onDestroy()
        stopService(Intent(this, BackgroundService::class.java))
    }


}