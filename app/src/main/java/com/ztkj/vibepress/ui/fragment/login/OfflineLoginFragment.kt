package com.ztkj.vibepress.ui.fragment.login

import android.os.Bundle
import com.ztkj.baselib.base.viewmodel.BaseViewModel
import com.ztkj.baselib.ext.nav
import com.ztkj.baselib.ext.navigateAction
import com.ztkj.vibepress.R
import com.ztkj.vibepress.app.base.BaseFragment
import com.ztkj.vibepress.app.util.CacheUtil
import com.ztkj.vibepress.app.util.SerialNumberUtil
import com.ztkj.vibepress.data.model.kEnum.AppType.OFFLINE
import com.ztkj.vibepress.databinding.FragmentOfflineLoginBinding
import com.ztkj.vibepress.viewmodel.state.LoginViewModel

class OfflineLoginFragment : BaseFragment<LoginViewModel, FragmentOfflineLoginBinding>() {


    override fun initView(savedInstanceState: Bundle?) {
        binding.vm = mViewModel
        binding.click = ProxyClick()
    }

    override fun lazyLoadData() {
        super.lazyLoadData()
        binding.serialNumber.setText(SerialNumberUtil.getSerial())
    }

    inner class ProxyClick {
        fun login() {
            CacheUtil.setIsLogin(true)
            CacheUtil.setAppType(OFFLINE)
            nav().navigateAction(R.id.action_loginFragment_to_homeFragment)
        }
    }
}