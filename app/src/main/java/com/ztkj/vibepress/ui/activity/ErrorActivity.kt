package com.ztkj.vibepress.ui.activity

import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import cat.ereza.customactivityoncrash.CustomActivityOnCrash
import com.ztkj.baselib.base.viewmodel.BaseViewModel
import com.ztkj.baselib.ext.view.clickNoRepeat
import com.ztkj.vibepress.app.base.BaseActivity
import com.ztkj.vibepress.app.ext.delayedLoading
import com.ztkj.vibepress.app.ext.init
import com.ztkj.vibepress.app.util.SettingUtil
import com.ztkj.vibepress.app.util.StatusBarUtil
import com.ztkj.vibepress.databinding.ActivityErrorBinding

class ErrorActivity : BaseActivity<BaseViewModel, ActivityErrorBinding>() {
    override fun initView(savedInstanceState: Bundle?) {
        binding.includeLayout.toolbar.init("发生错误")
        supportActionBar?.setBackgroundDrawable(ColorDrawable(SettingUtil.getColor(this)))
        StatusBarUtil.setColor(this, SettingUtil.getColor(this), 0)
        val config = CustomActivityOnCrash.getConfigFromIntent(intent)
        binding.errorRestart.clickNoRepeat {
            config?.run {
                CustomActivityOnCrash.restartApplication(this@ErrorActivity, this)
            }
        }

        delayedLoading(30 * 1000L) {
            config?.run {
                CustomActivityOnCrash.restartApplication(this@ErrorActivity, this)
            }
        }
    }

}
