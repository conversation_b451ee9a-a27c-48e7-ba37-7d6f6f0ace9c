package com.ztkj.vibepress.ui.fragment.setting

import android.content.ComponentName
import android.content.Intent
import android.content.ServiceConnection
import android.os.Bundle
import android.os.IBinder
import androidx.lifecycle.lifecycleScope
import com.ztkj.baselib.base.viewmodel.BaseViewModel
import com.ztkj.sensorlib.utils.NmeaAnalysisUtil
import com.ztkj.vibepress.R
import com.ztkj.vibepress.app.base.BaseFragment
import com.ztkj.vibepress.app.ext.initNormalClose
import com.ztkj.vibepress.data.model.bean.DiagnosisItem
import com.ztkj.vibepress.data.model.kEnum.ServiceDataType
import com.ztkj.vibepress.databinding.FragmentDiagnosisBinding
import com.ztkj.vibepress.service.BackgroundService
import com.ztkj.vibepress.ui.adapter.DiagnosisAdapter
import com.ztkj.vibepress.ui.adapter.SettingItemSpacingDecoration
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Named

/**
 * 诊断，就是采集站的数据
 */
@AndroidEntryPoint
class DiagnosisFragment : BaseFragment<BaseViewModel, FragmentDiagnosisBinding>() {

    @Inject
    lateinit var diagnosisAdapter: DiagnosisAdapter

    @Inject
    @Named("DiagnosisItem")
    lateinit var diagnosisItemSpacingDecoration: SettingItemSpacingDecoration

    private var boundService: BackgroundService? = null

    override fun initView(savedInstanceState: Bundle?) {
        initToolbar()
        initRecyclerView()
    }

    private fun initToolbar() {
        binding.toolbar.initNormalClose(this)
    }

    private fun initRecyclerView() {
        binding.rvData.run {
            adapter = diagnosisAdapter
            addItemDecoration(diagnosisItemSpacingDecoration)
        }
    }

    override fun lazyLoadData() {
        super.lazyLoadData()
        bindService()
        diagnosisAdapter.submitList(diagnosisData)
        addDiffResponseListener()

    }

    /**
     * 添加差分响应监听
     */
    private fun addDiffResponseListener() {
        NmeaAnalysisUtil.getInstance().cfResponseLiveData.observe(viewLifecycleOwner) {
            handleServiceData(ServiceDataType.DIFF_RESPONSE, it)
        }
    }

    /**
     * 处理从BackgroundService收到的数据数据
     */
    private fun handleServiceData(serviceDataType: ServiceDataType, data: String) {
        // 更新 UI
        val position = serviceDataType.ordinal
        diagnosisAdapter.getItem(position)?.value = data
        lifecycleScope.launch(Dispatchers.Main) {
            diagnosisAdapter.notifyItemChanged(position, "payload")
        }
    }


    private fun bindService() {
        val intent = Intent(requireContext(), BackgroundService::class.java)
        requireContext().bindService(intent, mServiceConnection, 0)
    }

    private fun unBindService() {
        boundService?.removeOnDataChangedCallback()
        boundService = null
        requireContext().unbindService(mServiceConnection)
    }


    private val mServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            // 这里是与 Service 连接成功时的回调方法
            // 在该回调方法中，可以获取对 Service 的引用，并调用其公共方法
            service?.run {
                // 获取 boundService 实例
                boundService = (service as BackgroundService.MyBinder).getService()
                boundService?.setOnDataChangedCallback { type, data ->
                    handleServiceData(type, data)
                }
            }

        }

        override fun onServiceDisconnected(name: ComponentName?) {
            // 这里是与 Service 断开连接时的回调方法
        }

    }

    override fun onDestroy() {
        super.onDestroy()

        unBindService()
    }

    private val diagnosisData: ArrayList<DiagnosisItem>
        get() = arrayListOf(
            DiagnosisItem(getString(R.string.gpgga_data)),
            DiagnosisItem(getString(R.string.serial_port_data)),
            DiagnosisItem(getString(R.string.report_data)),
            DiagnosisItem(getString(R.string.data_center_response)),
            DiagnosisItem(getString(R.string.diff_response))
        )


}