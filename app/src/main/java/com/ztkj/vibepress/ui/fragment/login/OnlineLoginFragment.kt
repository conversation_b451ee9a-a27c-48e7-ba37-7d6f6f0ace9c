package com.ztkj.vibepress.ui.fragment.login

import android.os.Bundle
import androidx.fragment.app.viewModels
import com.ztkj.baselib.ext.nav
import com.ztkj.baselib.ext.navigateAction
import com.ztkj.baselib.ext.parseState
import com.ztkj.baselib.ext.util.toMD5
import com.ztkj.vibepress.R
import com.ztkj.vibepress.app.base.BaseFragment
import com.ztkj.vibepress.app.ext.delayedLoading
import com.ztkj.vibepress.app.ext.showMessage
import com.ztkj.vibepress.app.util.CacheUtil
import com.ztkj.vibepress.app.util.SerialNumberUtil
import com.ztkj.vibepress.appViewModel
import com.ztkj.vibepress.data.model.kEnum.AppType.ONLINE
import com.ztkj.vibepress.databinding.FragmentOnlineLoginBinding
import com.ztkj.vibepress.viewmodel.request.RequestLoginRegisterViewModel
import com.ztkj.vibepress.viewmodel.state.DeviceSettingViewModel
import com.ztkj.vibepress.viewmodel.state.LoginViewModel
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class OnlineLoginFragment : BaseFragment<LoginViewModel, FragmentOnlineLoginBinding>() {

    private val requestLoginRegisterViewModel: RequestLoginRegisterViewModel by viewModels()

    private val deviceSettingViewModel: DeviceSettingViewModel by viewModels()

    override fun initView(savedInstanceState: Bundle?) {
        binding.vm = mViewModel
        binding.click = ProxyClick()
    }

    override fun lazyLoadData() {
        super.lazyLoadData()
        mViewModel.username.set(SerialNumberUtil.getSerial())
        mViewModel.password.set("123456")

        delayedLoading(timeMillis = 500L) {
            if (CacheUtil.isLogin()) {
                requestLogin()
            }
        }
    }

    override fun createObserver() {
        requestLoginRegisterViewModel.run {
            loginResult.observe(
                this@OnlineLoginFragment
            ) { resultState ->
                parseState(resultState, { loginClient ->
                    CacheUtil.setUser(loginClient)
                    //设置全局UserInfo
                    appViewModel.userInfo.value = loginClient
                    requestLoginRegisterViewModel.getDeviceAttribute(mViewModel.username.get())
                }, {
                    showMessage("登录失败，请重试!")
                })
            }

            deviceAttributeResult.observe(this@OnlineLoginFragment) { resultState ->
                parseState(resultState, {
                    CacheUtil.setAppType(ONLINE)
                    CacheUtil.setIsLogin(true)
                    deviceSettingViewModel.insertTampDeviceEntity(it)
                    appViewModel.tampDeviceEntity.value = it
                    nav().navigateAction(R.id.action_loginFragment_to_homeFragment)
                }, {
                    showMessage("获取设备信息失败")
                })
            }
        }
    }

    fun requestLogin() {
        requestLoginRegisterViewModel.loginReq(
            mViewModel.username.get(),
            mViewModel.password.get().toMD5()
        )
    }

    inner class ProxyClick {
        fun login() {
            when {
                mViewModel.username.get().isEmpty() -> showMessage("请填写账号")
                mViewModel.password.get().isEmpty() -> showMessage("请填写密码")
                else -> requestLogin()
            }
        }
    }

    
}