package com.ztkj.vibepress.ui.adapter

import android.content.Context
import android.graphics.Rect
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.ztkj.vibepress.data.model.bean.SettingItem
import com.ztkj.vibepress.databinding.ItemMainSettingBinding
import javax.inject.Inject

/**
 * @CreateTime : 2023/6/7 17:07
 * <AUTHOR> AppOS
 * @Description :
 */
class SettingAdapter @Inject constructor() : BaseQuickAdapter<SettingItem, SettingAdapter.VH>() {
    class VH(
        parent: ViewGroup,
        val binding: ItemMainSettingBinding = ItemMainSettingBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        ),
    ) : RecyclerView.ViewHolder(binding.root)

    override fun onBindViewHolder(holder: VH, position: Int, item: SettingItem?) {
        item?.let { settingItem ->
            with(holder.binding) {
                desc.text = settingItem.title
                innerDescBg.setImageResource(settingItem.resId)
            }
        }
    }

    override fun onCreateViewHolder(context: Context, parent: ViewGroup, viewType: Int): VH {
        return VH(parent)
    }

}

class SettingItemSpacingDecoration(
    private val top: Int = 0,
    private val left: Int = 0,
    private val bottom: Int = 0,
    private val right: Int = 0
) :
    RecyclerView.ItemDecoration() {

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        super.getItemOffsets(outRect, view, parent, state)
        outRect.top = top
        outRect.left = left
        outRect.bottom = bottom
        outRect.right = right
    }
}