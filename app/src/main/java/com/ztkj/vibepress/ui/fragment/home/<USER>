package com.ztkj.vibepress.ui.fragment.home


import android.annotation.SuppressLint
import android.os.Bundle
import android.util.Log
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.afollestad.materialdialogs.LayoutMode
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.bottomsheets.BottomSheet
import com.afollestad.materialdialogs.lifecycle.lifecycleOwner
import com.afollestad.materialdialogs.list.listItemsSingleChoice
import com.elvishew.xlog.XLog
import com.ztkj.baselib.ext.parseState
import com.ztkj.baselib.ext.util.setOnclickNoRepeat
import com.ztkj.baselib.ext.util.toJson
import com.ztkj.vibepress.app.base.BaseFragment
import com.ztkj.vibepress.app.ext.toast
import com.ztkj.vibepress.app.util.BenchmarkUtil
import com.ztkj.vibepress.app.util.CacheUtil
import com.ztkj.vibepress.appViewModel
import com.ztkj.vibepress.data.model.bean.JobStandardEntity
import com.ztkj.vibepress.data.model.bean.TampDeviceEntity
import com.ztkj.vibepress.databinding.FragmentHammerBinding
import com.ztkj.vibepress.eventViewModel
import com.ztkj.vibepress.viewmodel.request.HammerRequestViewModel
import com.ztkj.vibepress.viewmodel.state.HammerStateViewModel
import com.ztkj.vibepress.viewmodel.state.HammerViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import java.math.RoundingMode
import java.time.LocalDate
import javax.inject.Inject


/**
 * 首页夯机
 */
@AndroidEntryPoint
class HammerFragment : BaseFragment<HammerStateViewModel, FragmentHammerBinding>() {

    private val hammerViewModel: HammerViewModel by viewModels()

    private val hammerRequestViewModel: HammerRequestViewModel by viewModels()

    private var currentHammerHeight: Float? = null

    private var jobStandardEntity: JobStandardEntity? = null

    private var tampDeviceEntity: TampDeviceEntity? = null

    private var currentElevationHeight: Float? = null

    override fun initView(savedInstanceState: Bundle?) {
        binding.vm = mViewModel
        binding.click = ProxyClick()
        jobStandardEntity = appViewModel.jobStandardEntity.value
        tampDeviceEntity = appViewModel.tampDeviceEntity.value
        showWorkType(jobStandardEntity)
//        setupElevationView()
    }

    override fun lazyLoadData() {
        super.lazyLoadData()
        lifecycleScope.launch {
            hammerViewModel.getTampDeviceEntityFlow().collect() { entity ->
                appViewModel.tampDeviceEntity.value = entity
            }
        }

        lifecycleScope.launch(Dispatchers.IO) {
            while (isActive) {
                hammerRequestViewModel.getHjProcessEntity(appViewModel.tampDeviceEntity.value?.id)
                delay(5 * 1000)
            }
        }
    }

    override fun createObserver() {
        super.createObserver()
        appViewModel.run {
            jobStandardEntity.observeInFragment(this@HammerFragment) {
                showWorkType(it)

            }

            hammerHeight.observeInFragment(this@HammerFragment) {
                if (currentHammerHeight != it) {
                    binding.hammerView.updateHeight(it)
                    mViewModel.hammerHeight.set("${it}m")
                    currentHammerHeight = it
                }
            }

            /*            //切换设备属性
                        tampDeviceEntity.observeInFragment(this@HammerFragment) {
                            setupElevationView()
                        }*/
        }

        /*        eventViewModel.run {
                    currentStakeNumber.observeInFragment(this@HammerFragment) {
                        mViewModel.currentStake.set(it)
                    }

                    currentHitCount.observeInFragment(this@HammerFragment) {
                        mViewModel.realHitCount.set(it)
                    }
                }*/

        hammerRequestViewModel.run {
            processEntityResult.observe(this@HammerFragment) { resultState ->
                parseState(resultState, {
                    if (it.isNotEmpty()) {
                        val processEntity = it[0]
                        eventViewModel.currentStakeNumber.value = processEntity.stakeCode
                        eventViewModel.currentHitCount.value = processEntity.hitNumber.toString()
                        mViewModel.currentStake.set(processEntity.stakeCode)
                        mViewModel.realHitCount.set(processEntity.hitNumber.toString())
                    }
                }, {
                    it.printStackTrace()
                    XLog.d(it.message)
                })
            }

        }
    }

    private fun showWorkType(entity: JobStandardEntity?) {
        if (entity == null) {
            mViewModel.workTypeString.set("点夯")
            return
        }
        binding.tvJobType.text = entity.name
        mViewModel.hammerWeight.set(entity.hammerWeight.toString())
        mViewModel.requireEnergy.set(entity.requireEnergy.setScale(0, RoundingMode.DOWN).toString())
        mViewModel.workTypeString.set(entity.descTampWorkType)
        mViewModel.requireHitCount.set(entity.requireTimes.toString())
        val benchmarkHeight =
            BenchmarkUtil.calculateBenchmarkHeight(entity.requireEnergy, entity.hammerWeight)
        Log.e(TAG, "showWorkType: ${entity.toJson()}")
        binding.hammerView.updateElevationBenchmark(benchmarkHeight)
    }


    @SuppressLint("CheckResult")
    private fun showWorkTypeBottomSheet(entities: List<JobStandardEntity>) {
        //transform List<JobStandardEntity> to List<String>
        val data = entities.map { it.toVisibleString() }
        MaterialDialog(requireContext(), BottomSheet(LayoutMode.WRAP_CONTENT)).show {
            title(text = "请选择作业标准")
            listItemsSingleChoice(
                res = null,
                items = data,
                initialSelection = entities.indexOf(CacheUtil.getCurrentJobStandard())
            ) { _, index, _ ->
                val jobStandardEntity = entities[index]
                CacheUtil.setJobStandard(jobStandardEntity)
                appViewModel.jobStandardEntity.value = jobStandardEntity
                "切换作业标准成功".toast()
            }
            lifecycleOwner(viewLifecycleOwner)
        }
    }

    private fun changeCurrentJobStandard() {
        // Start collecting data from the hammerViewModel
        lifecycleScope.launch {
            // Show a bottom sheet  from the data stream
            hammerViewModel.getJobStandardList().collect() {
                showWorkTypeBottomSheet(it)
                // Cancel the coroutine to stop listening to the data stream
                this.cancel()
            }
        }
    }

    inner class ProxyClick {

        /**
         * 更改作业标准
         */
        fun changeJobStandard() {
            setOnclickNoRepeat(binding.tvJobType) {
                changeCurrentJobStandard()
            }
        }
    }

}