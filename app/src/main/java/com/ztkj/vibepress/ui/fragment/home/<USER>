package com.ztkj.vibepress.ui.fragment.home

import android.content.ComponentName
import android.content.Intent
import android.content.ServiceConnection
import android.os.Bundle
import android.os.IBinder
import android.util.Log
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.elvishew.xlog.XLog
import com.esri.arcgisruntime.geometry.Point
import com.esri.arcgisruntime.geometry.SpatialReferences
import com.google.gson.Gson
import com.ztkj.app.mapcore.BaseMapHelper
import com.ztkj.app.mapcore.GeoMapManager
import com.ztkj.app.mapcore.GeoMapManager.MapType.Map2D
import com.ztkj.baselib.ext.parseState
import com.ztkj.baselib.ext.util.notNull
import com.ztkj.baselib.ext.util.toJson
import com.ztkj.mapext.loadTdtImgLayer
import com.ztkj.sensorlib.model.GpsdataInfo
import com.ztkj.vibepress.R
import com.ztkj.vibepress.app.base.BaseFragment
import com.ztkj.vibepress.app.ext.delayedLoading
import com.ztkj.vibepress.app.ext.showMessage
import com.ztkj.vibepress.app.ext.showToast
import com.ztkj.vibepress.appViewModel
import com.ztkj.vibepress.data.Constants.MAP.Companion.WFS_TAMPING_TABLE_PREFIX
import com.ztkj.vibepress.data.Constants.MAP.Companion.WFS_TAMPING_URL
import com.ztkj.vibepress.data.Constants.MAP.Companion.WMS_DESIGN_POINT_TABLE_NAME
import com.ztkj.vibepress.data.Constants.MAP.Companion.WMS_DESIGN_POINT_URL
import com.ztkj.vibepress.data.model.bean.TampGpsDetailEntity
import com.ztkj.vibepress.data.model.kEnum.ServiceDataType
import com.ztkj.vibepress.data.model.kEnum.ServiceDataType.GPS_INFO
import com.ztkj.vibepress.databinding.FragmentMapBinding
import com.ztkj.vibepress.eventViewModel
import com.ztkj.vibepress.service.BackgroundService
import com.ztkj.vibepress.viewmodel.request.RequestMapViewModel
import com.ztkj.vibepress.viewmodel.request.GpsDataRequestViewModel
import com.ztkj.vibepress.viewmodel.state.HomeViewModel
import com.ztkj.vibepress.viewmodel.state.MapStateViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

@AndroidEntryPoint
class MapFragment : BaseFragment<MapStateViewModel, FragmentMapBinding>() {

    private val requestMapViewModel: RequestMapViewModel by viewModels()

    private val gpsDataRequestViewModel: GpsDataRequestViewModel by viewModels()

    /**
     * MapView Helper Tools
     */
    private lateinit var helper: BaseMapHelper

    private var boundService: BackgroundService? = null

    //上一次的位置
    private var lastPosition: Point? = null


    override fun initView(savedInstanceState: Bundle?) {
        //databinding viewmodel
        binding.vm = mViewModel
        binding.click = ProxyClick()
        //init helper
        helper = GeoMapManager.buildHelper(Map2D)

//        mViewModel.show()

        initMap()

    }

    override fun lazyLoadData() {
        super.lazyLoadData()
        val center: Point =Point(112.96072247892924	, 27.287562182085438)
        helper.setMapCenter(center, 10.0)
        delayedLoading(1000L) {
            helper.setMapCenter(center, 30.0)
            //加载WMS 设计点图层
            helper.loadWmsLayer(WMS_DESIGN_POINT_URL, listOf(WMS_DESIGN_POINT_TABLE_NAME))
            //加载夯点图层
            val deptId = appViewModel.tampDeviceEntity.value?.deptId
            helper.loadRefreshWmsLayer(
                WMS_DESIGN_POINT_URL,
                listOf(WFS_TAMPING_TABLE_PREFIX + deptId),
                10 * 1000L,
                "status=2"
            )

        }
        helper.drawHammer(center,R.drawable.dcm,0.0f)
        //TODO 该用其他方式获取位置
        /*        delayedLoading {
                    bindService()
                }*/

        delayedLoading(10 * 1000L) {
            startPeriodicWfsLoading()
        }

        // 开始循环获取GPS数据
        startPeriodicGpsDataRequest()

    }

    /**
     * 加载最近的夯点
     */
    private fun loadNearestTampingPoint() {
        val deptId = appViewModel.tampDeviceEntity.value?.deptId
        helper.loadNearestTampingPoint(WFS_TAMPING_URL, WFS_TAMPING_TABLE_PREFIX + deptId) {
            it.notNull({ attributes ->
                val hitCount = attributes["hit_count"].toString()
                val stakeNumber = attributes["stake_code"].toString()
                if (stakeNumber.isBlank()) {
                    eventViewModel.currentStakeNumber.value = ""
                } else {
                    eventViewModel.currentStakeNumber.value = stakeNumber
                }

                eventViewModel.currentHitCount.value = hitCount
            }, {
                eventViewModel.currentHitCount.value = "0"
            })
        }
    }

    /**
     * 加载施工引导点
     */
    private fun loadWfsLayer() {
        val center = appViewModel.currentPosition.value
//        val center: Point =Point(112.96072247892924	, 27.287562182085438)
        if (center != null) {
            helper.loadWfsLayer(
                WFS_TAMPING_URL, WMS_DESIGN_POINT_TABLE_NAME, center.x, center.y
            ) {
/*            it.notNull({ attributes ->
                //设置当前作业的桩号
                val stakeNumber = attributes["stake_code"].toString()
                eventViewModel.currentStakeNumber.value = stakeNumber
            }, {
                eventViewModel.currentStakeNumber.value = ""
            })*/

            }
        }

    }


    /**
     * 定时加载夯点图层
     */
    private fun startPeriodicWfsLoading() {
        lifecycleScope.launch {
            while (isActive) {
                loadWfsLayer()
//                loadNearestTampingPoint()
                delay(10 * 1000)
            }
        }
    }


    private fun initMap() {
        //init map
        helper.init(binding.mapView)
        //load TianDiTu vector layer
        helper.loadTdtImgLayer()

        initMapTouchListener()
    }

    private fun initMapTouchListener() {
        helper.setMapOnTouchCallback { _, layerName, bbox ->
            requestMapViewModel.findFeatures(layerName, bbox)
        }
    }

    override fun createObserver() {
        super.createObserver()
        requestMapViewModel.featureResult.observe(this@MapFragment) { resultState ->
            parseState(resultState, {
                helper.loadNearbyDesignPoint(it)
                XLog.d(it.toJson())
            }, {
                it.printStackTrace()
                XLog.d(it.message)
            })
        }

        // 观察GPS数据请求结果
        gpsDataRequestViewModel.gpsDataResult.observe(this@MapFragment) { resultState ->
            parseState(resultState, { gpsData ->
                // GPS数据获取成功，处理数据
                handleGpsData(gpsData)
//                XLog.d("GPS数据获取成功: ${gpsData.toJson()}")
            }, { error ->
                // GPS数据获取失败
                error.printStackTrace()
                XLog.e("GPS数据获取失败: ${error.message}")
            })
        }

        appViewModel.currentPosition.observeInFragment(this@MapFragment) {
            if (lastPosition?.x != it.x || lastPosition?.y != it.y) {
                helper.drawHammer(it, R.drawable.dcm, 0.0f)
                lastPosition = it
            }
        }

        /**
         * 当前的夯点号
         */
        eventViewModel.currentStakeNumber.observeInFragment(this@MapFragment) {
            //加载夯点图层
            val deptId = appViewModel.tampDeviceEntity.value?.deptId
            helper.loadNearbyTampingPointList(WFS_TAMPING_URL, WFS_TAMPING_TABLE_PREFIX + deptId) {
                for (queryResult in it) {
                    val attributes = queryResult.attributes
                    val stakeNumber = attributes["stake_code"].toString()
                    if (stakeNumber == eventViewModel.currentStakeNumber.value) {
                        helper.drawHighlightPoint(
                            eventViewModel.currentHitCount.value,
                            queryResult.geometry
                        )
                        return@loadNearbyTampingPointList
                    }
                }
            }
            /*            helper.loadNearestTampingPoint(WFS_TAMPING_URL, WFS_TAMPING_TABLE_PREFIX + deptId) {
                            it.notNull({ result ->
                                XLog.tag("MapFragment").d(result.toJson())
                            }, {
                                XLog.tag("MapFragment").d("夯点为空")
                            })
                        }*/
        }
    }


    private fun handleServiceData(serviceDataType: ServiceDataType, data: String) {
        if (serviceDataType == GPS_INFO) {
            try {
                val gson = Gson()
                val gpsdataInfo = gson.fromJson(data, GpsdataInfo::class.java)
                val point = Point(
                    gpsdataInfo.longitude.toDouble(),
                    gpsdataInfo.latitude.toDouble(),
                    SpatialReferences.getWgs84()
                )
                //TODO 夯机暂时不考虑角度问题
                helper.drawHammer(point, R.drawable.dcm, 0.0f)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    private fun bindService() {
        val intent = Intent(requireContext(), BackgroundService::class.java)
        requireContext().bindService(intent, mServiceConnection, 0)
    }

    private fun unBindService() {
        boundService?.removeOnDataChangedCallback()
        boundService = null
        requireContext().unbindService(mServiceConnection)
    }


    private val mServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            // 这里是与 Service 连接成功时的回调方法
            // 在该回调方法中，可以获取对 Service 的引用，并调用其公共方法
            service?.run {
                // 获取 boundService 实例
                boundService = (service as BackgroundService.MyBinder).getService()
                boundService?.setOnDataChangedCallback { serviceDataType, data ->
                    handleServiceData(serviceDataType, data)
                }
            }

        }

        override fun onServiceDisconnected(name: ComponentName?) {
            // 这里是与 Service 断开连接时的回调方法
        }

    }

    /**
     * 开始循环获取GPS数据
     */
    private fun startPeriodicGpsDataRequest() {
        val deviceId = appViewModel.tampDeviceEntity.value?.id
        if (!deviceId.isNullOrEmpty()) {
            // 开始每3秒循环请求GPS数据
            gpsDataRequestViewModel.startPeriodicGpsDataRequest(deviceId, 3000L)
            XLog.d("开始循环获取GPS数据，设备ID: $deviceId")
        } else {
            XLog.w("设备ID为空，无法开始GPS数据循环请求")
        }
    }

    /**
     * 停止循环获取GPS数据
     */
    private fun stopPeriodicGpsDataRequest() {
        gpsDataRequestViewModel.stopPeriodicGpsDataRequest()
        XLog.d("停止循环获取GPS数据")
    }

    /**
     * 处理GPS数据
     * @param gpsData GPS数据实体
     */
    private fun handleGpsData(gpsData: TampGpsDetailEntity) {
        try {
            // 处理车辆位置信息
            val carLat = gpsData.carLatitude.toDoubleOrNull()
            val carLon = gpsData.carLongitude.toDoubleOrNull()

            if (carLat != null && carLon != null && carLat != 0.0 && carLon != 0.0) {
                val carPoint = Point(carLon, carLat, SpatialReferences.getWgs84())
                // 更新地图上的车辆位置
                helper.drawHammer(carPoint, R.drawable.dcm, 0.0f)
                // 更新全局位置信息
                appViewModel.currentPosition.value = carPoint

            }

            // 处理锤的位置信息（如果有效）
            val hammerLat = gpsData.hammerLatitude.toDoubleOrNull()
            val hammerLon = gpsData.hammerLongitude.toDoubleOrNull()

            if (hammerLat != null && hammerLon != null && hammerLat != -1.0 && hammerLon != -1.0) {
                // 如果锤的位置有效，可以在这里处理锤的位置显示
                XLog.d("锤位置: 经度=${hammerLon}, 纬度=${hammerLat}")
            }

        } catch (e: Exception) {
            XLog.e("处理GPS数据时发生错误: ${e.message}")
            e.printStackTrace()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        // 停止GPS数据循环请求
        stopPeriodicGpsDataRequest()
    }

    inner class ProxyClick {
        fun setting() {
        }

        fun zoomIn() {
            helper.mapZoomIn()
        }

        fun zoomOut() {
            helper.mapZoomOut()
        }
    }

}