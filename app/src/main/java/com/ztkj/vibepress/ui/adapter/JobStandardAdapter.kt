package com.ztkj.vibepress.ui.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.text.InputType
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.ViewGroup
import android.view.inputmethod.EditorInfo
import android.widget.EditText
import androidx.core.widget.doAfterTextChanged
import androidx.core.widget.doBeforeTextChanged
import androidx.core.widget.doOnTextChanged
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseMultiItemAdapter
import com.elvishew.xlog.XLog
import com.ztkj.vibepress.R
import com.ztkj.vibepress.app.ext.getString
import com.ztkj.vibepress.app.util.SerialNumberUtil
import com.ztkj.vibepress.app.util.VibeConfig
import com.ztkj.vibepress.data.model.bean.DeviceSettingEntity
import com.ztkj.vibepress.data.model.bean.FieldType
import com.ztkj.vibepress.data.model.kEnum.TampWorkType
import com.ztkj.vibepress.data.model.kEnum.TampWorkType.*
import com.ztkj.vibepress.databinding.ItemSettingDropdownListBinding
import com.ztkj.vibepress.databinding.ItemSettingEdittextBinding

/**
 * @CreateTime : 2023/6/15 16:05
 * <AUTHOR> AppOS
 * @Description : 作业标准Adapter
 */
class JobStandardAdapter(
    data: MutableList<DeviceSettingEntity>, var dataMap: MutableMap<String, String> = mutableMapOf(
        "workType" to "0",
        "requireDistance" to "4",
        "serialNum" to SerialNumberUtil.getSerial()
    )
) : BaseMultiItemAdapter<DeviceSettingEntity>(data) {

    //工作类型
    private var workType = POINT

    private var distanceResId: Int = R.array.point_distance


    class ItemTextFieldVH(val binding: ItemSettingEdittextBinding) :
        RecyclerView.ViewHolder(binding.root)

    class ItemDropdownListVH(val binding: ItemSettingDropdownListBinding) :
        RecyclerView.ViewHolder(binding.root)

    init {
        addItemType(
            FieldType.TEXT_FIELD.ordinal,
            object :
                OnMultiItemAdapterListener<DeviceSettingEntity, ItemTextFieldVH> {
                @SuppressLint("SetTextI18n")
                override fun onBind(
                    holder: ItemTextFieldVH, position: Int, item: DeviceSettingEntity?
                ) {
                    if (item == null) return
                    holder.binding.apply {
                        title.text = item.title + ":"
                        if (item.inputType != InputType.TYPE_NUMBER_FLAG_DECIMAL) {
                            edittext.inputType = item.inputType
                        }
                        edittext.maxLines = 1
                        edittext.hint = getString(R.string.please_enter) + item.title
                        edittext.setText(getContentProperty(item.title))
                        edittext.doAfterTextChanged {
                            setContentProperty(item.title, it.toString())
                        }
                    }
                    holder.binding.edittext.setOnEditorActionListener { _, actionId, _ ->
                        if (actionId == EditorInfo.IME_ACTION_NEXT) {
                            if (position < data.size - 1) {
                                // 获取下一个 Item 的 EditText 控件，并请求焦点
                                val nextItemEditText =
                                    (holder.itemView.parent as RecyclerView).findViewHolderForAdapterPosition(
                                        position + 1
                                    )?.itemView?.findViewById<EditText>(R.id.edittext)
                                nextItemEditText?.requestFocus()
                            }
                            true
                        } else {
                            false
                        }
                    }
                }

                override fun onCreate(
                    context: Context, parent: ViewGroup, viewType: Int
                ): ItemTextFieldVH {
                    val binding = ItemSettingEdittextBinding.inflate(
                        LayoutInflater.from(context), parent, false
                    )
                    return ItemTextFieldVH(binding)
                }

            }).addItemType(
            FieldType.DROPDOWN_LIST.ordinal,
            object :
                OnMultiItemAdapterListener<DeviceSettingEntity, ItemDropdownListVH> {
                @SuppressLint("SetTextI18n")
                override fun onBind(
                    holder: ItemDropdownListVH, position: Int, item: DeviceSettingEntity?
                ) {
                    if (item == null) return
                    holder.binding.title.text = item.title
                    if (item.title == getString(R.string.workType)) {
                        holder.binding.spinner.setItems(R.array.workType)
                    } else if (item.title == getString(R.string.requireDistance)) {
                        holder.binding.spinner.setItems(distanceResId)
                    }
                    holder.binding.spinner.run {
                        val spinnerValue = getSpinnerContentProperty(item.title)
                        text = spinnerValue
                        setOnSpinnerOutsideTouchListener { _, _ ->
                            dismiss()
                        }
                        setOnSpinnerItemSelectedListener<String> { _, oldItem, _, newItem ->
                            if (oldItem == newItem) {
                                return@setOnSpinnerItemSelectedListener
                            }
                            if (item.title == getString(R.string.workType)) {
                                val requireDistanceItem =
                                    data.find { it.title == getString(R.string.requireDistance) }
                                requireDistanceItem?.run {
                                    distanceResId =
                                        if (newItem == getString(R.string.pointEnergy)) {
                                            workType = POINT
                                            dataMap["requireDistance"] = "3"
                                            R.array.point_distance
                                        } else {
                                            workType = FULL
                                            dataMap["requireDistance"] = "0.5"
                                            R.array.full_lap
                                        }
                                    notifyItemChanged(data.indexOf(this))
                                }
                            }
                        }
                        doAfterTextChanged {
                            setDropListContentProperty(item.title, it.toString())
                        }
                    }

                }

                override fun onCreate(
                    context: Context, parent: ViewGroup, viewType: Int
                ): ItemDropdownListVH {
                    val binding = ItemSettingDropdownListBinding.inflate(
                        LayoutInflater.from(context), parent, false
                    )
                    return ItemDropdownListVH(binding)
                }

            }).onItemViewType { position, list -> // 根据数据，返回对应的 ItemViewType
            when (list[position].fieldType) {
                FieldType.DROPDOWN_LIST -> {
                    FieldType.DROPDOWN_LIST.ordinal
                }

                else -> {
                    FieldType.TEXT_FIELD.ordinal
                }
            }
        }
    }


    private fun getContentProperty(descName: String): String? {
        return dataMap[getFieldNameByDescription(descName)]
    }

    private fun setContentProperty(descName: String, fieldValue: String) {
        dataMap[getFieldNameByDescription(descName)] = fieldValue
    }

    private fun getSpinnerContentProperty(descName: String): String? {
        //先获取dataMap 字段的真实值
        val realValue = dataMap[getFieldNameByDescription(descName)]
        if (descName == getString(R.string.workType)) {
            workType = if (realValue == "1") FULL else POINT
            distanceResId = when (workType) {
                POINT -> R.array.point_distance
                FULL -> R.array.full_lap
            }
            return VibeConfig.getWorkTypeDescriptionNameByKey(realValue)
        } else if (descName == getString(R.string.requireDistance)) {
            return VibeConfig.getRequireDistanceDescNameByKey(realValue, workType)
        }
        return null
    }

    private fun setDropListContentProperty(descName: String, fieldValue: String) {
        if (descName == getString(R.string.workType)) {
            workType =
                if (fieldValue == getString(R.string.pointEnergy)) POINT else FULL
            dataMap[getFieldNameByDescription(descName)] =
                VibeConfig.getWorkTypeRealValue(workType).toString()
        } else if (descName == getString(R.string.requireDistance)) {
            dataMap[getFieldNameByDescription(descName)] =
                VibeConfig.getRequireDistanceRealValue(fieldValue, workType)
        }
    }

    /**
     * 验证数据是否合法
     */
    private fun validateData(): Boolean {
        return true
    }

    private fun getFieldNameByDescription(descName: String): String {
        return when (descName) {
            getString(R.string.name) -> "name"
            getString(R.string.workType) -> "workType"
            getString(R.string.requireEnergy) -> "requireEnergy"
            getString(R.string.requireDistance) -> "requireDistance"
            getString(R.string.requireTimes) -> "requireTimes"
            getString(R.string.threshold) -> "threshold"
            getString(R.string.clusterRadius) -> "clusterRadius"
            getString(R.string.fullJoint) -> "fullJoint"
            getString(R.string.fullThreshold) -> "fullThreshold"
            getString(R.string.pointClusterRadius) -> "pointClusterRadius"
            getString(R.string.fullClusterRadius) -> "fullClusterRadius"
            getString(R.string.hammerWeight) -> "hammerWeight"
            getString(R.string.hammerRadius) -> "hammerRadius"
            else -> ""
        }

    }


}