package com.ztkj.vibepress.ui.fragment.setting

import android.os.Bundle
import android.text.InputType
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.chad.library.adapter.base.QuickAdapterHelper
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.ztkj.baselib.base.appContext
import com.ztkj.baselib.ext.nav
import com.ztkj.baselib.ext.parseState
import com.ztkj.vibepress.R
import com.ztkj.vibepress.app.base.BaseFragment
import com.ztkj.vibepress.app.ext.hideSoftKeyboard
import com.ztkj.vibepress.app.ext.initNormalClose
import com.ztkj.vibepress.app.ext.showMessage
import com.ztkj.vibepress.appViewModel
import com.ztkj.vibepress.data.Constants.NAVIGATION_PARAMS.Companion.JOB_STANDARD_LIST_TO_DETAIL
import com.ztkj.vibepress.data.model.bean.DeviceSettingEntity
import com.ztkj.vibepress.data.model.bean.FieldType
import com.ztkj.vibepress.data.model.bean.JobStandardEntity
import com.ztkj.vibepress.data.model.kEnum.AppType
import com.ztkj.vibepress.databinding.FragmentJobStandardBinding
import com.ztkj.vibepress.ui.adapter.JobStandardAdapter
import com.ztkj.vibepress.ui.adapter.SettingItemSpacingDecoration
import com.ztkj.vibepress.viewmodel.request.RequestJobStandardViewModel
import com.ztkj.vibepress.viewmodel.state.JobStandardViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 作业标准管理
 */
@AndroidEntryPoint
class JobStandardFragment :
    BaseFragment<RequestJobStandardViewModel, FragmentJobStandardBinding>() {
    private val jobStandardViewModel: JobStandardViewModel by viewModels()

    private var jobStandardEntity: JobStandardEntity? = null


    private val helper by lazy(LazyThreadSafetyMode.NONE) {
        QuickAdapterHelper.Builder(jobStandardAdapter)
            .build()
    }

    private val jobStandardAdapter by lazy(LazyThreadSafetyMode.NONE) {
        JobStandardAdapter(jobStandardData)
    }


    override fun initView(savedInstanceState: Bundle?) {
        initToolbar()
        initRecyclerView()
        loadExistData()

    }

    private fun loadExistData() {
        arguments?.run {
            getLong(JOB_STANDARD_LIST_TO_DETAIL).let { uId ->
                lifecycleScope.launch(Dispatchers.IO) {
                    jobStandardEntity = jobStandardViewModel.getJobStandardEntityByUid(uId)
                    jobStandardEntity?.run {
                        val gson = Gson()
                        val jsonString = gson.toJson(jobStandardEntity)
                        val mapType = object : TypeToken<MutableMap<String, String>>() {}.type
                        val map: MutableMap<String, String> = gson.fromJson(jsonString, mapType)
                        jobStandardAdapter.dataMap = map
                    }
                }
            }
        }
    }

    private fun initToolbar() {
        binding.toolBar.run {
            initNormalClose(this@JobStandardFragment)
            rightButtonClick {
                saveLocalData()
            }
        }
    }

    private fun saveLocalData() {
        hideSoftKeyboard(mActivity)
        //这里的dataMap在之前应该先进行非空判断
        lifecycleScope.launch {
            val uId = jobStandardViewModel.insert(jobStandardAdapter.dataMap)
            if (uId == -1L) {
                return@launch
            }
            //在线版本的
            if (appViewModel.appType.value == AppType.ONLINE) {
                //根据uId 查到数据库的数据
                jobStandardEntity = jobStandardViewModel.getJobStandardEntityByUid(uId)
                jobStandardEntity?.run {
                    mViewModel.addOrUpdateJobStandard(this)
                }
            } else {
                nav().navigateUp()
            }
        }
    }

    private fun initRecyclerView() {
        binding.rvData.adapter = helper.adapter
        binding.rvData.addItemDecoration(
            SettingItemSpacingDecoration(
                top = 20,
                left = 32,
                right = 32
            )
        )
    }


    override fun createObserver() {
        super.createObserver()
        mViewModel.run {
            addOrUpdateResult.observe(viewLifecycleOwner) { resultState ->
                parseState(resultState, { dataId ->
                    jobStandardEntity?.run {
                        //修改服务器的ID
                        id = dataId
                        jobStandardViewModel.updateJobStandard(this)
                        nav().navigateUp()
                    }
                }, {
                    showMessage("保存失败,请检查网络设置!")
                })
            }
        }
        jobStandardViewModel.jobStandardError.observe(viewLifecycleOwner) {
            if (it) {
                showMessage(getString(R.string.jobStandard_error), title = "数据错误提示")
            }
        }
    }


    private val jobStandardData = arrayListOf(
        DeviceSettingEntity(
            appContext.getString(R.string.name),
            inputType = InputType.TYPE_CLASS_TEXT
        ),
        DeviceSettingEntity(
            appContext.getString(R.string.workType),
            fieldType = FieldType.DROPDOWN_LIST,
            spinnerList = R.array.workType
        ),
        DeviceSettingEntity(appContext.getString(R.string.requireEnergy)),
        DeviceSettingEntity(appContext.getString(R.string.requireTimes)),
        DeviceSettingEntity(
            appContext.getString(R.string.requireDistance),
            fieldType = FieldType.DROPDOWN_LIST
        ),
        DeviceSettingEntity(appContext.getString(R.string.hammerRadius)),
        DeviceSettingEntity(appContext.getString(R.string.hammerWeight)),
    )
}