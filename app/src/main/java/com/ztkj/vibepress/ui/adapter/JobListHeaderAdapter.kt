package com.ztkj.vibepress.ui.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseSingleItemAdapter
import com.chad.library.adapter.base.fullspan.FullSpanAdapterType
import com.ztkj.vibepress.R

/**
 * @CreateTime : 2023/6/20 17:21
 * <AUTHOR> AppOS
 * @Description :
 */

class JobListHeaderAdapter : BaseSingleItemAdapter<Any, JobListHeaderAdapter.VH>(),
    FullSpanAdapterType {
    class VH(view: View) : RecyclerView.ViewHolder(view)

    companion object {
        private const val HEAD_VIEWTYPE = 0x10000556
    }

    override fun onBindViewHolder(holder: VH, item: Any?) {

    }

    override fun onCreateViewHolder(context: Context, parent: ViewGroup, viewType: Int): VH {
        return VH(
            LayoutInflater.from(parent.context)
                .inflate(R.layout.layout_job_header_view, parent, false)
        )
    }

    override fun getItemViewType(position: Int, list: List<Any>): Int {
        return HEAD_VIEWTYPE
    }
}


