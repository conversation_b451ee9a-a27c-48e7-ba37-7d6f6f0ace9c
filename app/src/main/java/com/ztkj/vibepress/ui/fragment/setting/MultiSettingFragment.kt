package com.ztkj.vibepress.ui.fragment.setting

import android.content.ComponentName
import android.content.Intent
import android.content.ServiceConnection
import android.os.Bundle
import android.os.IBinder
import com.elvishew.xlog.XLog
import com.ztkj.baselib.ext.util.notNull
import com.ztkj.sensorlib.utils.SerialPortUtils
import com.ztkj.vibepress.R
import com.ztkj.vibepress.app.base.BaseFragment
import com.ztkj.vibepress.app.ext.initNormalClose
import com.ztkj.vibepress.app.ext.showAvailableBaudrate
import com.ztkj.vibepress.app.ext.showSerialPortDevicePathDialog
import com.ztkj.vibepress.app.ext.toast
import com.ztkj.vibepress.app.util.CacheUtil
import com.ztkj.vibepress.app.util.CalculateUtil
import com.ztkj.vibepress.app.util.CounterParser
import com.ztkj.vibepress.app.widget.recyclerview.SpaceItemDecoration
import com.ztkj.vibepress.data.Constants.SENSOR_COMMAND.Companion.COMMAND_READ
import com.ztkj.vibepress.data.model.bean.CounterParseEntity
import com.ztkj.vibepress.data.model.bean.SerialConfigBean
import com.ztkj.vibepress.data.model.kEnum.ServiceDataType
import com.ztkj.vibepress.data.model.kEnum.ServiceDataType.SENSOR_DATA
import com.ztkj.vibepress.databinding.FragmentMultiSettingBinding
import com.ztkj.vibepress.service.BackgroundService
import com.ztkj.vibepress.ui.adapter.SimpleAdapter
import com.ztkj.vibepress.viewmodel.state.MultiSettingViewModel
import dagger.hilt.android.AndroidEntryPoint
import java.lang.ref.WeakReference
import javax.inject.Inject


@AndroidEntryPoint
class MultiSettingFragment : BaseFragment<MultiSettingViewModel, FragmentMultiSettingBinding>() {
    private lateinit var serialConfigBean: SerialConfigBean

    @Inject
    lateinit var simpleAdapter: SimpleAdapter

    private var boundService: BackgroundService? = null

    override fun initView(savedInstanceState: Bundle?) {
        binding.click = ProxyClick()
        binding.vm = mViewModel
        initToolbar()
        initRecyclerView()
        addRadioButtonListener()
        setSpinnerView()
        addCheckboxListener()
    }

    override fun lazyLoadData() {
        super.lazyLoadData()
        serialConfigBean = CacheUtil.getSerialConfig()
        binding.config = serialConfigBean
        bindService()
    }


    private fun initToolbar() {
        binding.toolbar.initNormalClose(this)
    }

    private fun initRecyclerView() {
        binding.rvData.run {
            simpleAdapter.animationEnable = false
            adapter = simpleAdapter
            addItemDecoration(SpaceItemDecoration(4, 2, true))
        }
    }

    private fun addRadioButtonListener() {
        binding.radioGroupSensor.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                R.id.radio_button_3 -> {
                    mViewModel.boardType.value = ServiceDataType.GNSS_ALL
                }

                R.id.radio_button_4 -> {
                    mViewModel.boardType.value = SENSOR_DATA
                }

                else -> throw IllegalArgumentException("UnKnown checkedId")
            }
        }
    }

    /**
     * 设置PowerSpinnerView
     */
    private fun setSpinnerView() {
        binding.powerOffSpinner.setOnSpinnerItemSelectedListener<String> { oldIndex, _, newIndex, newItem ->
            if (oldIndex == newIndex) {
                return@setOnSpinnerItemSelectedListener
            }
            mViewModel.powerOffClear.set(newItem == "是")
        }

        binding.spinnerCounterReversing.setOnSpinnerItemSelectedListener<String> { oldIndex, _, newIndex, newItem ->
            if (oldIndex == newIndex) {
                return@setOnSpinnerItemSelectedListener
            }
            mViewModel.counterReversing.set(newItem == "是")
        }

    }

    private fun addCheckboxListener() {
        binding.checkboxSettingMode.setOnCheckedChangeListener { _, isChecked ->
            boundService?.receiveCommand(
                SENSOR_DATA,
                "raw ${if (isChecked) 1 else 0} \r\n"
            )
        }
    }

    override fun createObserver() {
        super.createObserver()

        mViewModel.run {
            serviceStatus.observe(viewLifecycleOwner) {
                binding.btnStartOrStopService.text = if (it) "停止服务" else "开启服务"
            }
            gnssValue.observe(viewLifecycleOwner) {
                if (boardType.value == ServiceDataType.GNSS_ALL) {
                    simpleAdapter.add(it)
                    checkNeedToShowLatest()
                }
            }
            counterValue.observe(viewLifecycleOwner) {
                if (boardType.value == SENSOR_DATA) {
                    simpleAdapter.add(it)
                    checkNeedToShowLatest()
                }
                //收到了计数传感器读取设置的响应
                if (it.startsWith("\$C")) {
                    handleReadSensorSettingResponse(it)
                } else {
                    val pair = CalculateUtil.parseSensorCounter(it)
                    if (pair?.first != null) {
                        mViewModel.sensorDataValue.set(pair.first)
                    }
                }

            }
        }
    }

    private fun handleReadSensorSettingResponse(data: String) {
        val counterParseEntity = CounterParser.parseCounterData(data)
        XLog.d("counterParseEntity:$counterParseEntity")
        counterParseEntity.notNull({
            setSensorViewWithData(it)
        }, {
            "读取传感器设置失败，请稍候重试!!!".toast()
        })
    }

    private fun setSensorViewWithData(counterParseEntity: CounterParseEntity) {
        mViewModel.powerOffClear.set(counterParseEntity.zero == 1)
        mViewModel.counterReversing.set(counterParseEntity.counterReversing == 1)
        mViewModel.updateFrequency.set(counterParseEntity.frequency)
        mViewModel.tensionThreshold.set(counterParseEntity.tensionThreshold)
        binding.spinnerCounterReversing.text =
            if (counterParseEntity.counterReversing == 1) "是" else "否"
        binding.powerOffSpinner.text = if (counterParseEntity.zero == 1) "是" else "否"
    }

    private fun checkNeedToShowLatest() {
        if (simpleAdapter.itemCount > 1) {
            binding.rvData.smoothScrollToPosition(simpleAdapter.itemCount - 1)
        }
    }

    private fun notifyPropertyChanged() {
        //TODO 这里的notifyPropertyChange没有起作用
//        binding.notifyPropertyChanged(BR.config)
        //改为直接修改
        binding.config = serialConfigBean
    }

    private fun handleServiceData(serviceDataType: ServiceDataType, data: String) {
        if (serviceDataType == ServiceDataType.GNSS_ALL) {
            mViewModel.gnssValue.postValue(data)
        } else if (serviceDataType == SENSOR_DATA) {
            mViewModel.counterValue.postValue(data)
        }
    }


    private fun bindService() {
        val intent = Intent(requireContext(), BackgroundService::class.java)
        requireContext().bindService(intent, mServiceConnection, 0)
    }

    override fun onDestroy() {
        super.onDestroy()
        unBindService()
    }

    private fun unBindService() {
        try {
            requireContext().unbindService(mServiceConnection)
            boundService?.removeOnDataChangedCallback()
            boundService = null
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 开启服务
     */
    private fun startService() {
        val intent = Intent(requireContext(), BackgroundService::class.java)
        requireContext().startService(intent)
    }

    /**
     * 停止服务
     */
    private fun stopService() {
        val intent = Intent(requireContext(), BackgroundService::class.java)
        requireContext().stopService(intent)
    }


    private val mServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            // 这里是与 Service 连接成功时的回调方法
            // 在该回调方法中，可以获取对 Service 的引用，并调用其公共方法
            service?.run {
                // 获取 boundService 实例
                boundService = (service as BackgroundService.MyBinder).getService()
                mViewModel.serviceStatus.postValue(true)
                boundService?.setOnDataChangedCallback { serviceDataType, data ->
                    handleServiceData(serviceDataType, data)
                }
            }

        }


        override fun onServiceDisconnected(name: ComponentName?) {
            // 这里是与 Service 断开连接时的回调方法
            XLog.d("与Service断开连接")
            mViewModel.serviceStatus.postValue(false)
        }

    }

    inner class ProxyClick {

        /**
         * 读取传感器设置
         * result: eg: $C,29,1.00,0.00,0,0*65
         */
        fun readSensorSetting() {
            if (mViewModel.serviceStatus.value == true) {
                boundService?.receiveCommand(SENSOR_DATA, COMMAND_READ)
//                doAfterSendCommandClick()
            } else {
                "请先启动服务".toast()
                return
            }
        }

        /**
         * 保存传感器设置
         */
        fun saveSensorSetting() {
            CacheUtil.setSerialConfig(serialConfigBean)
            if (mViewModel.serviceStatus.value == true) {
                val zeroCommand = if (binding.powerOffSpinner.text.toString() == "是") 1 else 0
                boundService?.receiveCommand(
                    SENSOR_DATA,
                    "set zero $zeroCommand \r\n"
                )
                val counterReversingCommand =
                    if (binding.spinnerCounterReversing.text.toString() == "是") 1 else 0
                boundService?.receiveCommand(
                    SENSOR_DATA,
                    "set inc $counterReversingCommand \r\n"
                )
                val updateFrequencyCommand = mViewModel.updateFrequency.get()
                boundService?.receiveCommand(
                    SENSOR_DATA,
                    "set frq $updateFrequencyCommand \r\n"
                )
                val tensionThresholdCommand = mViewModel.tensionThreshold.get()
                boundService?.receiveCommand(
                    SENSOR_DATA,
                    "set yz $tensionThresholdCommand \r\n"
                )
                val settingMode = if (binding.checkboxSettingMode.isChecked) 1 else 0
                boundService?.receiveCommand(
                    SENSOR_DATA,
                    "raw $settingMode \r\n"
                )
                boundService?.receiveCommand(
                    SENSOR_DATA,
                    "reset \r\n"
                )

                "保存成功".toast()
            }

        }

        fun saveConfig() {
            CacheUtil.setSerialConfig(serialConfigBean)
        }

        /**
         * 开启或关闭服务
         */
        fun startOrStopService() {
            if (mViewModel.serviceStatus.value == true) {
                unBindService()
                stopService()
                mViewModel.serviceStatus.value = false
                "服务已停止".toast()
            } else {
                startService()
                bindService()
                mViewModel.serviceStatus.value = true
            }
        }

        /**
         * 内置GNSS路径
         */
        fun internalGnssPath() {
            showSerialPortDevicePathDialog(
                WeakReference(this@MultiSettingFragment),
                SerialPortUtils.getAllDevicesPath().toList().indexOf(serialConfigBean.rtkPath)
            ) { _, _, textValue ->
                serialConfigBean.rtkPath = textValue
                notifyPropertyChanged()
            }
        }

        /**
         * 内置GNSS波特率
         */
        fun internalGnssBaudrate() {
            showAvailableBaudrate(
                WeakReference(this@MultiSettingFragment),
                SerialPortUtils.getAllBaudrate().toList().indexOf(serialConfigBean.rtkBaudrate)
            ) { _, _, textValue ->
                serialConfigBean.rtkBaudrate = textValue
                notifyPropertyChanged()
            }
        }

        /**
         * 外置GNSS路径
         */
        fun externalGnssPath() {
            showSerialPortDevicePathDialog(
                WeakReference(this@MultiSettingFragment),
                SerialPortUtils.getAllDevicesPath().toList()
                    .indexOf(serialConfigBean.externalGnssPath)
            ) { _, _, textValue ->
                serialConfigBean.externalGnssPath = textValue
                notifyPropertyChanged()
            }
        }

        /**
         *  外置GNSS波特率
         */
        fun externalGnssBaudrate() {
            showAvailableBaudrate(
                WeakReference(this@MultiSettingFragment),
                SerialPortUtils.getAllBaudrate().toList()
                    .indexOf(serialConfigBean.externalGnssBaudrate)
            ) { _, _, textValue ->
                serialConfigBean.externalGnssBaudrate = textValue
                notifyPropertyChanged()
            }
        }


        fun sensorPath() {
            showSerialPortDevicePathDialog(
                WeakReference(this@MultiSettingFragment),
                SerialPortUtils.getAllDevicesPath().toList().indexOf(serialConfigBean.counterPath)
            ) { _, _, textValue ->
                serialConfigBean.counterPath = textValue
                notifyPropertyChanged()
            }
        }

        fun sensorBaudrate() {
            showAvailableBaudrate(
                WeakReference(this@MultiSettingFragment),
                SerialPortUtils.getAllBaudrate().toList()
                    .indexOf(serialConfigBean.counterBaudrate)
            ) { _, _, textValue ->
                serialConfigBean.counterBaudrate = textValue
                notifyPropertyChanged()
            }
        }


    }

}