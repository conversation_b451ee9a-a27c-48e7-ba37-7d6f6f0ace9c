package com.ztkj.vibepress.ui.fragment.setting

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.text.InputType
import com.afollestad.materialdialogs.MaterialDialog
import com.afollestad.materialdialogs.input.input
import com.blankj.utilcode.util.AppUtils
import com.xuexiang.xupdate.easy.EasyUpdate
import com.xuexiang.xupdate.proxy.impl.DefaultUpdateChecker
import com.ztkj.baselib.base.viewmodel.BaseViewModel
import com.ztkj.baselib.ext.nav
import com.ztkj.vibepress.R
import com.ztkj.vibepress.app.base.BaseFragment
import com.ztkj.vibepress.app.ext.initNormalClose
import com.ztkj.vibepress.app.ext.showMessage
import com.ztkj.vibepress.app.ext.showToast
import com.ztkj.vibepress.app.network.AppRetrofit
import com.ztkj.vibepress.app.util.CacheUtil
import com.ztkj.vibepress.appViewModel
import com.ztkj.vibepress.data.Constants.CONFIG.Companion.ADMIN_SETTING_PASSWORD
import com.ztkj.vibepress.data.Constants.UPDATE.Companion.APP_UPDATE_URL
import com.ztkj.vibepress.databinding.FragmentAboutBinding
import com.ztkj.vibepress.service.BackgroundService

class AboutFragment : BaseFragment<BaseViewModel, FragmentAboutBinding>() {
    override fun initView(savedInstanceState: Bundle?) {
        binding.click = ProxyClick()
        initToolbar()
    }

    private fun initToolbar() {
        binding.toolbar.initNormalClose(this@AboutFragment)
    }

    override fun lazyLoadData() {
        super.lazyLoadData()
        val versionName = AppUtils.getAppVersionName()
        binding.tvVersion.text = getString(R.string.checkUpdate, versionName)
    }

    private fun clearExitCache() {
        AppRetrofit.INSTANCE.cookieJar.clear()
        CacheUtil.setUser(null)
        appViewModel.userInfo.value = null
    }


    inner class ProxyClick {

        fun exitLogin() {
            showMessage(
                message = getString(R.string.confirm_exit_message),
                negativeButtonText = getString(R.string.cancel),
                positiveAction = {
                    clearExitCache()
                    requireActivity().stopService(
                        Intent(
                            requireContext(),
                            BackgroundService::class.java
                        )
                    )
//                    nav().navigate(R.id.action_settingFragment_to_loginFragment)
                    nav().popBackStack(R.id.loginFragment, false)
                }
            )
        }

        /**
         * 高级系统设置
         */
        @SuppressLint("CheckResult")
        fun advancedSystemSetting() {
            var password = ""
            MaterialDialog(requireContext()).show {
                title(R.string.enterAdminPassword)
                input(
                    hint = "管理员密码",
                    inputType = InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_FLAG_CAP_WORDS
                ) { _, text ->
                    password = text.toString()
                }
                positiveButton(R.string.confirm)
                negativeButton(R.string.cancel)
                positiveButton {
                    if (password.equals(ADMIN_SETTING_PASSWORD, true)) {
                        nav().navigate(R.id.action_aboutFragment_to_advancedSettingFragment)
                    } else {
                        showToast("密码错误,请联系管理员!")
                    }
                }
            }

        }

        fun checkUpdate() {
            EasyUpdate.create(requireContext(), APP_UPDATE_URL)
                .updateChecker(object : DefaultUpdateChecker() {
                    override fun noNewVersion(throwable: Throwable?) {
                        super.noNewVersion(throwable)
                        showToast(R.string.no_new_version)
                    }
                })
                .update()
        }
    }
}
