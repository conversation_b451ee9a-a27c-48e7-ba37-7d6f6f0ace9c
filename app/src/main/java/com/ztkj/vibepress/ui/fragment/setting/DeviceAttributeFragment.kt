package com.ztkj.vibepress.ui.fragment.setting

import android.annotation.SuppressLint
import android.os.Bundle
import android.text.InputType
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.ztkj.baselib.base.viewmodel.BaseViewModel
import com.ztkj.baselib.ext.nav
import com.ztkj.vibepress.R
import com.ztkj.vibepress.app.base.BaseFragment
import com.ztkj.vibepress.app.ext.hideSoftKeyboard
import com.ztkj.vibepress.app.ext.initNormalClose
import com.ztkj.vibepress.app.ext.showMessage
import com.ztkj.vibepress.app.ext.toast
import com.ztkj.vibepress.data.model.bean.DeviceSettingEntity
import com.ztkj.vibepress.data.model.bean.FieldType
import com.ztkj.vibepress.data.model.bean.TampDeviceEntity
import com.ztkj.vibepress.databinding.FragmentDeviceAttributeBinding
import com.ztkj.vibepress.ui.adapter.DeviceSettingAdapter
import com.ztkj.vibepress.ui.adapter.SettingItemSpacingDecoration
import com.ztkj.vibepress.viewmodel.state.DeviceSettingViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 监控设备属性
 */
@AndroidEntryPoint
class DeviceAttributeFragment : BaseFragment<BaseViewModel, FragmentDeviceAttributeBinding>() {

    private val deviceSettingViewModel: DeviceSettingViewModel by viewModels()

    private lateinit var tampDeviceEntity: TampDeviceEntity


    private var deviceSettingAdapter: DeviceSettingAdapter? = null

    override fun initView(savedInstanceState: Bundle?) {
        initToolBar()
        initRecyclerView()
    }

    private fun initToolBar() {
        binding.toolBar.run {
            initNormalClose(this@DeviceAttributeFragment)
            rightButtonClick {
                saveLocalData()
            }
        }
    }

    private fun saveLocalData() {
        hideSoftKeyboard(mActivity)
        
        /*       if (deviceSettingAdapter?.dataMap?.size < settingData.size) {
                   "请输入所有必要信息".toast()
               } else {

               }*/
        deviceSettingViewModel.insert(deviceSettingAdapter?.dataMap)

    }

    @SuppressLint("NotifyDataSetChanged")
    override fun lazyLoadData() {
        super.lazyLoadData()
        lifecycleScope.launch(Dispatchers.IO) {
            val deviceMap = deviceSettingViewModel.getTampDeviceDeviceMap()
//            Log.e(TAG, "lazyLoadData: ${deviceMap.toJson()}")
            withContext(Dispatchers.Main) {
                deviceSettingAdapter =
                    DeviceSettingAdapter(settingData, deviceMap ?: mutableMapOf())
                initRecyclerView()
            }
        }
    }

    private fun initRecyclerView() {
        binding.rvData.adapter = deviceSettingAdapter
        binding.rvData.addItemDecoration(SettingItemSpacingDecoration(8, 20, 0, 20))
    }

    override fun createObserver() {
        super.createObserver()
        deviceSettingViewModel.updateStatus.observe(this@DeviceAttributeFragment) { success ->
            if (success) {
                "保存成功".toast()
                nav().navigateUp()
            } else {
                "保存失败，请检查网络设置".toast()
            }
        }
        deviceSettingViewModel.deviceAttributeError.observe(this@DeviceAttributeFragment) {
            if (it) {
                showMessage(getString(R.string.deviceAttribute_error), title = "数据错误提示")
            }
        }
    }

    private val settingData: ArrayList<DeviceSettingEntity>
        get() = arrayListOf(
            DeviceSettingEntity(
                getString(R.string.device_name),
                inputType = InputType.TYPE_CLASS_TEXT
            ),
            DeviceSettingEntity(getString(R.string.verticalDistance)),
            DeviceSettingEntity(getString(R.string.horizontalDistance)),
            DeviceSettingEntity(
                getString(R.string.installsWay),
                fieldType = FieldType.DROPDOWN_LIST,
                spinnerList = R.array.install_way
            ),
            DeviceSettingEntity(getString(R.string.antennaHeight)),
            DeviceSettingEntity(getString(R.string.cableCount)),
            DeviceSettingEntity(getString(R.string.magnetNum)),
            DeviceSettingEntity(getString(R.string.windingEnginePerimeter)),
            DeviceSettingEntity(getString(R.string.magnetDistance)),
            DeviceSettingEntity(getString(R.string.hitThreshold)),
            DeviceSettingEntity(getString(R.string.clusterThreshold)),
            DeviceSettingEntity(getString(R.string.tensionThreshold)),

            )

}
