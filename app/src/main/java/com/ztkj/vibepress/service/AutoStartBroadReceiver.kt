package com.ztkj.vibepress.service

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.elvishew.xlog.XLog
import com.ztkj.vibepress.ui.activity.SplashActivity

/**
 * @CreateTime : 2023/5/29 8:53
 * <AUTHOR> AppOS
 * @Description :
 */
class AutoStartBroadReceiver : BroadcastReceiver() {

    override fun onReceive(context: Context?, intent: Intent?) {
        XLog.d("Receive Broadcast", intent)
        //Boot Startup
        intent?.action?.takeIf { ACTION_BOOT_COMPLETED == it }?.let {
            val splashActivityIntent = Intent(context, SplashActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
            context?.startActivity(splashActivityIntent)
        }
    }

    companion object {
        private const val ACTION_BOOT_COMPLETED = "android.intent.action.BOOT_COMPLETED"
    }
}