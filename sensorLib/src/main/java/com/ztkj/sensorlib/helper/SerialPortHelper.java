package com.ztkj.sensorlib.helper;

import android.serialport.SerialPort;
import android.util.Log;

import com.blankj.utilcode.util.StringUtils;
import com.ztkj.sensorlib.utils.ByteUtil;
import com.ztkj.sensorlib.utils.NmeaAnalysisUtil;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;

/**
 * @CreateTime : 2022/9/5 11:45
 * <AUTHOR> AppOS
 * @Description :
 */
public abstract class SerialPortHelper {
    private static final String TAG = "SerialPortHelper";

    private SerialPort serialPort;

    private OutputStream mOutStream;

    private SerialPort mSerialPort;
    private OutputStream mOutputStream;
    private InputStream mInputStream;
    private SerialPortHelper.ReadThread mReadThread;
    private SerialPortHelper.WriteThread mWriteThread;
    private String sPort = "/dev/ttyS0";
    private int iBaudRate = 15200;
    private int stopBits = 1;
    private int dataBits = 8;
    private int parity = 0;
    private int flowCon = 0;
    private int flags = 0;
    private boolean _isOpen = false;
    private byte[] _bLoopData = new byte[]{48};
    private int iDelay = 500;
    private BufferedReader serialBufferedReader;
    private final static int RESULT_MIN_LENGTH = 8;
    private byte[] mBuffer;


    public SerialPortHelper(String port, int baudRate) throws IOException {
        this.sPort = port;
        this.iBaudRate = baudRate;
        mSerialPort = SerialPort.newBuilder(sPort, iBaudRate).build();

    }

    protected abstract void onDataReceived(NmeaAnalysisUtil data, String command);

    public void open() throws IOException {
        mInputStream = mSerialPort.getInputStream();
        mOutputStream = mSerialPort.getOutputStream();
        serialBufferedReader = new BufferedReader(new InputStreamReader(mInputStream));
        mReadThread = new ReadThread();
        mReadThread.start();
        _isOpen = true;
    }

    public Boolean isOpen() {
        return _isOpen;
    }

    public void close() throws IOException {
        mReadThread.interrupt();
        mWriteThread.interrupt();
        mInputStream.close();
        mOutputStream.close();
        serialBufferedReader.close();
        _isOpen = false;
    }

    public void sendTxt(String sTxt) {
        byte[] data = ByteUtil.hexStr2bytes(sTxt);
        sendData(data);
    }


    public void sendData(byte[] data) {
        if (!_isOpen) return;
        if (mOutputStream != null) {
            try {
                mOutputStream.write(data);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private class WriteThread extends Thread {
        @Override
        public void run() {
            super.run();
            while (!isInterrupted()) {
                try {
                    if (mOutputStream != null) {
                        mOutputStream.write(mBuffer);
                    } else {
                        return;
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                    return;
                }
            }
        }
    }

    private class ReadThread extends Thread {

        @Override
        public void run() {
            super.run();
            while (!isInterrupted()) {
                try {
                    if (mInputStream == null) return;
                    //读取的定位数据
                    String tempStr = serialBufferedReader.readLine();
                    String[] results = tempStr.split(",");
                    if (!StringUtils.isEmpty(tempStr)) {
                        if (results.length > RESULT_MIN_LENGTH) {
                            NmeaAnalysisUtil nmeaAnalysisUtil = NmeaAnalysisUtil.getInstance();
                            nmeaAnalysisUtil.processNmeaData(tempStr);
                            onDataReceived(nmeaAnalysisUtil, tempStr);

                        }
                    }

                } catch (IOException e) {
                    e.printStackTrace();
                    return;
                }

                try {
                    Thread.sleep(50L);
                } catch (InterruptedException var4) {
                    var4.printStackTrace();
                }
            }
        }
    }

}
