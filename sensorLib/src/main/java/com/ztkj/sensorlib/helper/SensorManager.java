package com.ztkj.sensorlib.helper;

import com.ztkj.sensorlib.model.SensorType;

/**
 * @CreateTime : 2022/10/10 11:02
 * <AUTHOR> AppOS
 * @Description :
 */
public class SensorManager {
    private static SensorType sensorType = SensorType.ZNYS;

    private final static SensorManager mInstance = new SensorManager();


    public static SensorManager getInstance(){
        return mInstance;
    }

    public  void init(SensorType type){
        sensorType = type;
    }

    public SensorType getSensorType(){
        return sensorType;
    }
}
