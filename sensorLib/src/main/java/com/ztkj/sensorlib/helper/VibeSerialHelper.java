package com.ztkj.sensorlib.helper;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

import tp.xmaihh.serialport.SerialHelper;
import tp.xmaihh.serialport.bean.ComBean;

/**
 * @CreateTime : 2023/5/17 19:17
 * <AUTHOR> AppOS
 * @Description :
 */
public class VibeSerialHelper {
    private final SerialHelper serialHelper;
    private static final String TAG = "VibeSerialHelper";

    public interface OnDataReceivedCallback{
        void onDataReceived(String vibeData);
    }

    public VibeSerialHelper(String sPort, int iBaudRate, VibeSerialHelper.OnDataReceivedCallback callback) {
        serialHelper = new SerialHelper(sPort,iBaudRate) {
            @Override
            protected void onDataReceived(ComBean comBean) {
                handleVibePressData(callback,comBean);
            }
        };
    }

    private void handleVibePressData(VibeSerialHelper.OnDataReceivedCallback callback, ComBean comBean){
        if (callback != null){
            String _data = new String(comBean.bRec, StandardCharsets.UTF_8);
            callback.onDataReceived(_data);
        }
    }

    /**
     * 发送数据
     * @param sTxt
     */
    public void sendTxt(String sTxt) {
        serialHelper.sendTxt(sTxt);
    }

    public void sendHex(String sHex) {
        serialHelper.sendHex(sHex);
    }

    public void send(byte[] bOutArray) {
        serialHelper.send(bOutArray);
    }


    public String getPort(){
        if (serialHelper == null){
            return  "";
        }
        return serialHelper.getPort();
    }

    public boolean isOpen(){
        if (serialHelper == null){
            return false;
        }
        return serialHelper.isOpen();
    }

    public void open() throws IOException {
        serialHelper.open();
    }

    public void close(){
        serialHelper.close();
    }
}
