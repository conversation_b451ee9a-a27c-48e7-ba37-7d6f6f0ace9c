package com.ztkj.sensorlib.utils;

import android.serialport.SerialPortFinder;

import java.util.Collections;
import java.util.HashSet;

/**
 * @CreateTime : 2022/9/23 16:23
 * <AUTHOR> AppOS
 * @Description :
 */
public class SerialPortUtils {

    private static String[] getAllDevices() {
        SerialPortFinder serialPortFinder = new SerialPortFinder();
        return serialPortFinder.getAllDevices();
    }


    //["/dev/ttyGS3","/dev/ttyGS2","/dev/ttyGS1","/dev/ttyGS0","/dev/ttyUSB4","/dev/ttyUSB3","/dev/ttyUSB2","/dev/ttyUSB1","/dev/ttyUSB0","/dev/ttyS6","/dev/ttyS7","/dev/ttyS0","/dev/ttyS5","/dev/ttyS2","/dev/ttyS3","/dev/ttyS4"]
    public static String[] getAllDevicesPath() {
        SerialPortFinder serialPortFinder = new SerialPortFinder();
        String[] allDevicesPath = serialPortFinder.getAllDevicesPath();
        // 使用 HashSet 进行去重
        HashSet<String> distinctDevices = new HashSet<>();
        Collections.addAll(distinctDevices, allDevicesPath);
        // 将去重后的 HashSet 转换为字符串数组
        return distinctDevices.toArray(new String[0]);
    }


    /**
     * @return 获取所有的波特率
     */
    public static String[] getAllBaudrate() {
        return new String[]{"9600", "14400", "19200", "38400", "57600", "115200", "230400"};
//        return new String[]{"100","300","600","1200","2400","4800","9600","14400","19200","38400","56000","57600","115200","128000","256000"};
    }


}
