package com.ztkj.sensorlib.utils;

import android.util.Log;

import com.qy.led.MaterialBuffer;

import java.text.DecimalFormat;
import java.text.NumberFormat;

/**
 * @CreateTime : 2022/10/11 10:23
 * <AUTHOR> AppOS
 * @Description :
 */
public class SpeedUtils {
    private static final String TAG = "SpeedUtils";

    public static byte[] getLedBytesData(double speedK){
        Double speedMin = speedK * 1000 / 60;
        if(speedMin<=0.8)
            speedMin = 0d;
        NumberFormat nf=new DecimalFormat( "0.0 ");
        String speed = nf.format(speedMin);
        Log.e(TAG, "speed: " + speed);
        return getLedData(speed);
    }

    private static byte[] getLedData(String szShowContent){
        return  getLedData(szShowContent,2);
    }

    public static byte[] getLedData(String szShowContent,int nUID){
        byte[] byContent = MaterialBuffer.CollectionBuffer(szShowContent, nUID, false, false, (short)2, (short)2, (short)1, (short)1);
        return MaterialBuffer.CompletePacketBuffer(byContent, (short)101, 0);
    }
}
