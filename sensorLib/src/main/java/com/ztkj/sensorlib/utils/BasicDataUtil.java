package com.ztkj.sensorlib.utils;

public class BasicDataUtil {
	//-------------------------------------------------------
	// 判断奇数或偶数，位运算，最后一位是1则为奇数，为0是偶数
	public static int isOdd(final int num)
	{
		return num & 0x1;
	}
    //-------------------------------------------------------
	public static int HexToInt(final String inHex)//Hex字符串转int
    {
    	return Integer.parseInt(inHex, 16);
    }
    //-------------------------------------------------------
	public static byte HexToByte(final String inHex)//Hex字符串转byte
    {
    	return (byte)Integer.parseInt(inHex,16);
    }
    //-------------------------------------------------------
	public static String ByteToHex(final Byte inByte)//1字节转2个Hex字符
    {
    	return String.format("%02x", inByte).toUpperCase();
    }
    
    //-------------------------------------------------------
    //字节数组转hex字符串
	static public String ByteArrToHex(final byte[] inBytArr) {
		//检查输入源是否为null
		if(null == inBytArr) {
			Exception e = new IllegalArgumentException("Input null exception! Input is null when Convert byte array to hex string!");
			e.printStackTrace();
			return "";
		}
		
		//检查输入源长度
		int len = inBytArr.length;
		if(0 == len) {
			return "";
		}
		
		StringBuilder strBuilder=new StringBuilder();
		strBuilder.append(ByteToHex(inBytArr[0]));
		for (int i = 1; i < len; i++)
		{
			strBuilder.append(" ");
			strBuilder.append(ByteToHex(inBytArr[i]));
		}
		return strBuilder.toString(); 
	}
	
	//字节数组转hex字符串，可选长度
    static public String ByteArrToHex(final byte[] inBytArr,final int offset,final int byteCount) {
		//检查输入源是否为null
    	if(null == inBytArr) {
			Exception e = new IllegalArgumentException("Input null exception! Input is null when Convert byte array to hex string!");
			e.printStackTrace();
			return "";
		}
    	//检查输入参数是否合法
		int len=inBytArr.length;
		if((0 > offset) || (offset > len) || (0 > byteCount ) || (offset + byteCount) > len) {
			Exception e = new IllegalArgumentException("Input parameters exception! One at lest of the input parameters illegal when Convert byte array to hex string!");
			e.printStackTrace();
			return "";
		}
		//提取需要转换的部分
		byte[] tempBytes = new byte[byteCount];
		System.arraycopy(inBytArr, offset, tempBytes, 0, byteCount);
		//转换并返回
		return ByteArrToHex(tempBytes);
	}
    
	//-------------------------------------------------------
	//hex字符串转字节数组
	public static byte[] HexToByteArr(final String inHex) {
    	
		String srcHexStr = new String(inHex);
		int hexlen = srcHexStr.length();
		byte[] result;
		if (isOdd(hexlen)==1)
		{//奇数
			hexlen++;
			result = new byte[(hexlen/2)];
			srcHexStr="0" + srcHexStr;
		}else {//偶数
			result = new byte[(hexlen/2)];
		}
	    int j=0;
		for (int i = 0; i < hexlen; i+=2)
		{
			result[j]=HexToByte(srcHexStr.substring(i,i+2));
			j++;
		}
	    return result; 
	}
    
	//-------------------------------------------------------
    //双精度浮点数转带 DecimalNum 位小数的字符串
	public static String DoubleToString(final double inDouble, final int DecimalNum) {
    	
		int decimalNum;
    	if (DecimalNum < 0) {					//判断输入的小数位数
    		decimalNum = 0;
    	} else {
    		decimalNum = DecimalNum;
    	}
		String sf = String.format("%%.%df", decimalNum);
    	return String.format(sf, inDouble);
    }
    
	//-------------------------------------------------------
    //单精度浮点数转带 DecimalNum 位小数的字符串
	public static String FloatToString(final float inFloat, final int DecimalNum) {
    	
		int decimalNum;
    	if (DecimalNum < 0) {					//判断输入的小数位数
    		decimalNum = 0;
    	} else {
    		decimalNum = DecimalNum;
    	}
		String sf = String.format("%%.%df", decimalNum);
    	return String.format(sf, inFloat);
    }
	
	//-------------------------------------------------------
    //检查字符串是否为空（包括空引用和空内容）
	public static boolean isEmpty(final String srcStr) {
		
		if(null == srcStr) {					//判断是否为空引用
			return true;
		} else if("".equals(srcStr)) {			//判断是否为空内容
			return true;
		}
		return false;	
	}
}