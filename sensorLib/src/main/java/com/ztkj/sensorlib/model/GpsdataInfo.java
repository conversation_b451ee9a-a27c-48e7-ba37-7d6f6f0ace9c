package com.ztkj.sensorlib.model;

public class GpsdataInfo {

    private String gpsTime;

    private String longitude;

    private String latitude;

    private String altitude;  //高程

    private String solution;

    private String amp; //

    private String freq; //

    private String cmv; //

    private String satnum; //卫星数

    private String hdop;

    private String ellipsoid; //椭球面高

    private String diffdelay; //差分延迟

    private String diffstation; //差分站号

    private String devicedir; //方向

    private String speed;

    private String tempearature; //

    public GpsdataInfo() {
    }

    public String getGpsTime() {
        return gpsTime;
    }

    public void setGpsTime(String gpsTime) {
        this.gpsTime = gpsTime;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getAltitude() {
        return altitude;
    }

    public void setAltitude(String altitude) {
        this.altitude = altitude;
    }

    public String getSolution() {
        return solution;
    }

    public void setSolution(String solution) {
        this.solution = solution;
    }

    public String getAmp() {
        return amp;
    }

    public void setAmp(String amp) {
        this.amp = amp;
    }

    public String getFreq() {
        return freq;
    }

    public void setFreq(String freq) {
        this.freq = freq;
    }

    public String getCmv() {
        return cmv;
    }

    public void setCmv(String cmv) {
        this.cmv = cmv;
    }

    public String getSatnum() {
        return satnum;
    }

    public void setSatnum(String satnum) {
        this.satnum = satnum;
    }

    public String getHdop() {
        return hdop;
    }

    public void setHdop(String hdop) {
        this.hdop = hdop;
    }

    public String getEllipsoid() {
        return ellipsoid;
    }

    public void setEllipsoid(String ellipsoid) {
        this.ellipsoid = ellipsoid;
    }

    public String getDiffdelay() {
        return diffdelay;
    }

    public void setDiffdelay(String diffdelay) {
        this.diffdelay = diffdelay;
    }

    public String getDiffstation() {
        return diffstation;
    }

    public void setDiffstation(String diffstation) {
        this.diffstation = diffstation;
    }

    public String getDevicedir() {
        return devicedir;
    }

    public void setDevicedir(String devicedir) {
        this.devicedir = devicedir;
    }

    public String getSpeed() {
        return speed;
    }

    public void setSpeed(String speed) {
        this.speed = speed;
    }

    public String getTempearature() {
        return tempearature;
    }

    public void setTempearature(String tempearature) {
        this.tempearature = tempearature;
    }

    @Override
    public String toString() {
        return "GpsdataInfo{" +
                "gpsTime='" + gpsTime + '\'' +
                ", longitude='" + longitude + '\'' +
                ", latitude='" + latitude + '\'' +
                ", altitude='" + altitude + '\'' +
                ", solution='" + solution + '\'' +
                ", amp='" + amp + '\'' +
                ", freq='" + freq + '\'' +
                ", cmv='" + cmv + '\'' +
                ", satnum='" + satnum + '\'' +
                ", hdop='" + hdop + '\'' +
                ", ellipsoid='" + ellipsoid + '\'' +
                ", diffdelay='" + diffdelay + '\'' +
                ", diffstation='" + diffstation + '\'' +
                ", devicedir='" + devicedir + '\'' +
                ", speed='" + speed + '\'' +
                ", tempearature='" + tempearature + '\'' +
                '}';
    }
}
