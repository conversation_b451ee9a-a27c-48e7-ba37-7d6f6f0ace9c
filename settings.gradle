pluginManagement {
    repositories {
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }
        google()
        mavenCentral()
        gradlePluginPortal()

    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        maven { url 'https://jitpack.io' }
        //arcgis
        maven {
            url 'https://esri.jfrog.io/artifactory/arcgis'
        }
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/jcenter' }

        google()
        mavenCentral()

    }
}
rootProject.name = "VibePress"
include ':app'
include ':mapCore'
include ':baseLib'
include ':mapExt'
include ':imLib'
include ':imLibExt'
include ':sensorLib'
