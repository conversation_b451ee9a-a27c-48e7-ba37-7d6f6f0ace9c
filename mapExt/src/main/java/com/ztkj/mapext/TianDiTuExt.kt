package com.ztkj.mapext

import com.esri.arcgisruntime.layers.WebTiledLayer
import com.ztkj.app.mapcore.BaseMapHelper
import com.ztkj.app.mapcore.util.TianDiTuTiledLayerClass

/**
 * @CreateTime : 2023/4/23 15:51
 * <AUTHOR> AppOS
 * @Description :
 */

/**
 * 天地图矢量地图
 */
 fun BaseMapHelper.loadTdtVecLayer() {
    val baseLayers = mMapView.map.basemap.baseLayers
    baseLayers.clear()

    val vecWLayer: WebTiledLayer =
        TianDiTuTiledLayerClass.createTianDiTuTiledLayer(TianDiTuTiledLayerClass.LayerType.TIANDITU_VECTOR_2000)
    vecWLayer.name = "天地图矢量底图"
    baseLayers.add(vecWLayer)

    val cvaWLayer: WebTiledLayer =
        TianDiTuTiledLayerClass.createTianDiTuTiledLayer(TianDiTuTiledLayerClass.LayerType.TIANDITU_VECTOR_2000_LABLE)
    cvaWLayer.name = "天地图矢量注记"
    baseLayers.add(cvaWLayer)
}

/**
 * 天地图影像地图
 */
fun BaseMapHelper.loadTdtImgLayer() {
    val baseLayers = mMapView.map.basemap.baseLayers
    baseLayers.clear()

    val vecWLayer: WebTiledLayer =
        TianDiTuTiledLayerClass.createTianDiTuTiledLayer(TianDiTuTiledLayerClass.LayerType.TIANDITU_IMAGE_2000)
    vecWLayer.name = "天地图影像底图"
    baseLayers.add(vecWLayer)

    val cvaWLayer: WebTiledLayer =
        TianDiTuTiledLayerClass.createTianDiTuTiledLayer(TianDiTuTiledLayerClass.LayerType.TIANDITU_IMAGE_2000_LABLE)
    cvaWLayer.name = "天地图矢量注记"
    baseLayers.add(cvaWLayer)
}