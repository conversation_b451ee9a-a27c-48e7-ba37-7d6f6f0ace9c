package com.ztkj.mapext.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.Map;

/**
 * @CreateTime : 2022/7/22 9:32
 * <AUTHOR> AppOS
 * @Description : JsonUtils
 */
public class JsonUtils {
    /**
     * object转map
     *
     * @param obj
     * @return
     */
    public static Map<String, Object> objToMap(Object obj) {
        Gson gson = new Gson();
        Type type = new TypeToken<Map<String, Object>>() {
        }.getType();
        return gson.fromJson(gson.toJson(obj), type);
    }

    /**
     * object转字符串对象map
     *
     * @param obj
     * @return
     */
    public static Map<String, String> objToStrMap(Object obj) {
        return (Map<String, String>) JSON.toJSON(obj);
    }


    /**
     * object转json
     *
     * @param obj
     * @return
     */
    public static String objToJson(Object obj) {
        return JSON.toJSONString(obj);
    }

    //内存复用
    private static JSONObject jsonObject = null;

    /**
     * 根据key创建JSONObject，返回json
     *
     * @param key   传入的key
     * @param value 传入的对象
     * @return
     */
    public static String keyToJson(String key, Object value) {
        if (jsonObject == null) {
            jsonObject = new JSONObject();
        }
        jsonObject.clear();
        jsonObject.put(key, value);
        return jsonObject.toJSONString();
    }
}
