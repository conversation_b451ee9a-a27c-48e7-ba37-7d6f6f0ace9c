package com.ztkj.mapext

import android.graphics.BitmapFactory
import android.graphics.Color
import android.graphics.drawable.BitmapDrawable
import android.util.Log
import com.esri.arcgisruntime.data.FeatureQueryResult
import com.esri.arcgisruntime.data.QueryParameters
import com.esri.arcgisruntime.data.ServiceFeatureTable
import com.esri.arcgisruntime.geometry.Point
import com.esri.arcgisruntime.layers.FeatureLayer
import com.esri.arcgisruntime.loadable.LoadStatus
import com.esri.arcgisruntime.mapping.view.Graphic
import com.esri.arcgisruntime.ogc.wfs.OgcAxisOrder
import com.esri.arcgisruntime.ogc.wfs.WfsFeatureTable
import com.esri.arcgisruntime.symbology.PictureMarkerSymbol
import com.esri.arcgisruntime.symbology.SimpleLineSymbol
import com.esri.arcgisruntime.symbology.SimpleMarkerSymbol
import com.esri.arcgisruntime.symbology.SimpleRenderer
import com.esri.arcgisruntime.symbology.UniqueValueRenderer
import com.ztkj.app.mapcore.BaseMapHelper
import com.ztkj.app.mapcore.Map2DHelper
import com.ztkj.app.mapcore.entity.GeoJsonModel
import com.ztkj.app.mapcore.util.SymbolUtil
import com.ztkj.baselib.base.appContext
import com.ztkj.baselib.ext.util.toJson
import com.ztkj.mapext.util.GeoJsonUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext


/**
 * @CreateTime : 2023/5/6 9:34
 * <AUTHOR> AppOS
 * @Description : 强夯机地图扩展
 */

fun BaseMapHelper.loadVpFeatureLayer(url: String) {
    val serviceFeatureTable = ServiceFeatureTable(url)
    val featureLayer = FeatureLayer(serviceFeatureTable)
    val simpleMarkerSymbol =
        SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.TRANSPARENT, 10f)
    simpleMarkerSymbol.outline =
        SimpleLineSymbol(SimpleLineSymbol.Style.SOLID, Color.GRAY, 1f)
    val simpleRenderer = SimpleRenderer(simpleMarkerSymbol)
//    featureLayer.renderer = simpleRenderer

    //定义两个符号化规则，分别用于不同的筛选条件
    val symbol1 = SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.BLUE, 10f)
    val symbol2 = SimpleMarkerSymbol(SimpleMarkerSymbol.Style.CIRCLE, Color.RED, 10f)
    val renderer = UniqueValueRenderer()
    renderer.fieldNames.addAll(listOf("objectid"))

    renderer.defaultSymbol = symbol1 //如果没有匹配的值，将使用符号1作为默认符号
    renderer.defaultLabel = "ys"
//    featureLayer.definitionExpression = "objectid < 2800" //设置筛选条件

    featureLayer.renderer = renderer //设置渲染规则

    featureLayer.loadAsync()
    mMapView.map.operationalLayers.add(featureLayer)

}

fun BaseMapHelper.addListener() {

    mMapView.addMapScaleChangedListener {
        Log.e("MapView", "addListener: ${mMapView.mapScale},${mMapView.visibleArea.extent.center}")
    }
}

fun BaseMapHelper.drawTampingPoint(geoJsonModel: GeoJsonModel) {

    CoroutineScope(Dispatchers.IO).launch {
        val parseGeoJson = GeoJsonUtil.parseGeoJson(geoJsonModel)
        withContext(Dispatchers.Main) {
            parseGeoJson?.run {
//                graphicsOverlay.graphics.addAll(this)
                if (this.isNotEmpty()) {
                    mMapView.setViewpointCenterAsync(
                        this[0].geometry.extent.center,
                        500.0
                    )
                }
            }
        }
    }


}

/**
 * 查找最近的夯点
 */
fun BaseMapHelper.findNearestHammerPoint(location: Point) {
}